# -*- coding: utf-8 -*-

"""
value_averaging_strategy.py 代码分析结果
基于 init 和 handlebar 两个入口函数的调用链分析
"""

# ==================== 调用链分析 ====================

# 从 init 函数开始的调用链
INIT_CALL_CHAIN = {
    "init": [
        "init_database",
        "load_strategy_status", 
        "validate_strategy_state",
        "reset_strategy_state",  # 当验证失败时调用
        "force_activate_strategy",  # 当FORCE_ACTIVE_MODE=True时调用
        "log_message"
    ],
    
    "init_database": [
        "upgrade_database_schema",
        "create_database_indexes"
    ],
    
    "force_activate_strategy": [
        "calculate_latest_activation_point",
        "manual_set_active_status",
        "insert_simulated_buy_signal_with_info",
        "log_message"
    ],
    
    "calculate_latest_activation_point": [
        "get_historical_data_for_analysis",
        "calculate_ema",
        "log_message"
    ]
}

# 从 handlebar 函数开始的调用链
HANDLEBAR_CALL_CHAIN = {
    "handlebar": [
        "should_execute_strategy",
        "get_current_time_str", 
        "is_backtest_mode",
        "validate_strategy_state",
        "detect_signals",
        "execute_trading_logic",
        "task_queue_process_pending_tasks",
        "update_strategy_status",
        "get_strategy_performance_summary",
        "log_message",
        "record_skip_period"  # 异常时调用
    ],
    
    "detect_signals": [
        "get_historical_data_for_analysis",
        "resample_daily_to_period", 
        "calculate_ema",
        "check_signal_filter",
        "record_signal_to_db",
        "log_message"
    ],
    
    "execute_trading_logic": [
        "execute_phase_transition",
        "execute_value_averaging_strategy",
        "log_message"
    ],
    
    "execute_phase_transition": [
        "is_trade_time_allowed",
        "get_current_position",
        "execute_trade_order",
        "execute_buy_order_async",
        "log_message"
    ],
    
    "execute_value_averaging_strategy": [
        "is_adjustment_time",
        "is_trade_time_allowed", 
        "get_current_price",
        "calculate_value_averaging",
        "execute_buy_order_async",
        "execute_trade_order",
        "update_last_adjustment_period",
        "log_message"
    ],
    
    "task_queue_process_pending_tasks": [
        "TradeTaskExecutor.process_pending_tasks",
        "TradeTaskCallbackHandler.handle_order_callback",
        "TradeTaskCallbackHandler.handle_deal_callback"
    ]
}

# ==================== 未被调用的函数分析 ====================

# 完全未被调用的函数（从入口函数无法到达）
UNUSED_FUNCTIONS = [
    # 测试和调试函数
    "test_anti_duplicate_mechanism",
    "print_trade_logs_summary", 
    "query_trade_logs_by_kline_date",
    
    # 备用/废弃函数
    "calculate_ema_backup",  # 注释掉的备用函数
    "insert_simulated_buy_signal",  # 被更高级版本替代
    
    # 独立工具函数（可能被外部调用）
    "retry_on_failure",  # 装饰器，可能被其他地方使用
    "safe_execute",  # 工具函数，可能被其他地方使用
    "cleanup_resources",  # 清理函数，可能在程序退出时调用
    "handle_critical_error",  # 错误处理，可能在异常情况下调用
    "get_error_statistics",  # 统计函数，可能被外部查询
    
    # 数据重采样相关（部分未使用）
    "resample_by_data_distribution",  # 被 resample_daily_to_period 内部调用
    "group_by_quarters",  # 被 resample_by_data_distribution 调用
    "group_by_months",   # 被 resample_by_data_distribution 调用
    "adjust_to_trading_dates",  # 被 resample_by_data_distribution 调用
    "simple_resample_to_quarterly",  # 备用重采样方法
    
    # 账户和持仓相关（部分未使用）
    "get_all_positions",  # 获取所有持仓，可能被外部调用
    "record_account_info",  # 记录账户信息，可能在特定场景使用
    "record_position",  # 记录持仓，可能在特定场景使用
    "get_available_510720_amount",  # 获取510720可用金额
    
    # 交易执行相关（部分未使用）
    "execute_buy_order",  # 被新的异步系统替代
    "execute_normal_buy",  # 被新的异步系统替代  
    "execute_margin_buy",  # 被新的异步系统替代
    "execute_sell_order",  # 被新的异步系统替代
    "execute_backtest_trade",  # 回测交易，在特定模式下使用
    "simulate_position_update",  # 模拟持仓更新
    "record_position_change",  # 记录持仓变化
    
    # 价格获取相关
    "get_backtest_current_price",  # 被 get_current_price 内部调用
    "get_realtime_current_price",  # 被 get_current_price 内部调用
    
    # 策略状态和统计
    "get_strategy_phase_duration",  # 获取策略阶段持续时间
    "get_activation_statistics",  # 获取激活统计
    
    # 时间和周期判断
    "is_last_trading_day_of_month",  # 被 is_period_adjustment_day 调用
    "is_month_end_trading_day",  # 月末交易日判断
    
    # 持仓计算
    "calculate_position_from_trades",  # 从交易记录计算持仓
    
    # 历史数据和信号
    "get_historical_highest_price",  # 被 calculate_value_averaging 调用
    
    # 交易订单管理
    "record_trade_order",  # 记录交易订单
    "update_trade_order_status",  # 更新订单状态
    
    # 回调函数（由iQuant平台调用）
    "order_callback",  # iQuant订单回调
    "deal_callback",   # iQuant成交回调
    "task_queue_order_callback",  # 任务队列订单回调
    "task_queue_deal_callback",   # 任务队列成交回调
]

# ==================== 类分析 ====================

# 定义的类
DEFINED_CLASSES = [
    "TradeTaskQueue",      # 交易任务队列管理器
    "TradeTaskExecutor",   # 交易任务执行器  
    "TradeTaskCallbackHandler"  # 交易任务回调处理器
]

# 类的使用情况
CLASS_USAGE = {
    "TradeTaskQueue": {
        "instantiated": True,  # g_trade_task_queue = TradeTaskQueue()
        "used_in": ["execute_buy_order_async", "task_queue_process_pending_tasks"],
        "methods_used": [
            "create_task_group",
            "log_task_message", 
            "create_account_snapshot",
            "calculate_fees",
            "create_task"
        ]
    },
    
    "TradeTaskExecutor": {
        "instantiated": True,  # g_trade_task_executor = TradeTaskExecutor()
        "used_in": ["task_queue_process_pending_tasks"],
        "methods_used": [
            "process_pending_tasks",
            "execute_task",
            "update_task_status",
            "execute_sell_task",
            "execute_buy_cash_task", 
            "execute_buy_margin_task",
            "place_sell_order",
            "place_buy_order",
            "place_margin_buy_order",
            "check_timeout_tasks"
        ]
    },
    
    "TradeTaskCallbackHandler": {
        "instantiated": True,  # g_trade_task_callback_handler = TradeTaskCallbackHandler()
        "used_in": ["task_queue_order_callback", "task_queue_deal_callback"],
        "methods_used": [
            "handle_order_callback",
            "handle_deal_callback",
            "find_task_by_order_id",
            "find_task_by_order_uuid",
            "process_order_callback",
            "process_deal_callback",
            "complete_task",
            "record_callback_data",
            "sync_trade_orders_status"
        ]
    }
}

# ==================== 潜在可清理的代码 ====================

# 可以安全删除的函数（确认未被使用且无外部依赖）
SAFE_TO_DELETE = [
    "test_anti_duplicate_mechanism",  # 测试函数
    "calculate_ema_backup",  # 注释掉的备用函数
    "insert_simulated_buy_signal",  # 被更高级版本替代
]

# 可能可以删除的函数（需要进一步确认）
POTENTIALLY_DELETABLE = [
    "print_trade_logs_summary",  # 日志打印函数，可能被外部脚本使用
    "query_trade_logs_by_kline_date",  # 查询函数，可能被外部脚本使用
    "simple_resample_to_quarterly",  # 备用重采样方法
    "get_available_510720_amount",  # 可能在特定场景使用
    "get_strategy_phase_duration",  # 统计函数
    "get_activation_statistics",  # 统计函数
    "is_month_end_trading_day",  # 时间判断函数
]

# 保留的函数（虽然当前未直接调用，但有重要用途）
SHOULD_KEEP = [
    # 错误处理和工具函数
    "retry_on_failure", "safe_execute", "cleanup_resources", 
    "handle_critical_error", "get_error_statistics",
    
    # 回调函数（由外部平台调用）
    "order_callback", "deal_callback", 
    "task_queue_order_callback", "task_queue_deal_callback",
    
    # 账户和持仓管理
    "get_all_positions", "record_account_info", "record_position",
    "calculate_position_from_trades",
    
    # 旧交易系统（向后兼容）
    "execute_buy_order", "execute_normal_buy", "execute_margin_buy", 
    "execute_sell_order", "execute_backtest_trade",
    
    # 数据处理和分析
    "resample_by_data_distribution", "group_by_quarters", "group_by_months",
    "adjust_to_trading_dates", "get_historical_highest_price",
    
    # 订单管理
    "record_trade_order", "update_trade_order_status"
]

print("代码分析完成！")
print(f"总函数数量: {len(UNUSED_FUNCTIONS) + len([f for chain in INIT_CALL_CHAIN.values() for f in chain]) + len([f for chain in HANDLEBAR_CALL_CHAIN.values() for f in chain])}")
print(f"未被调用函数数量: {len(UNUSED_FUNCTIONS)}")
print(f"可安全删除函数数量: {len(SAFE_TO_DELETE)}")
print(f"可能可删除函数数量: {len(POTENTIALLY_DELETABLE)}")
