#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 order_uuid 字段集成
验证数据库升级、订单记录、回调处理等功能
"""

import sqlite3
import datetime
import uuid
import sys
import os

# 添加当前目录到路径
sys.path.append('.')

DATABASE_PATH = "gytrading2.db"

def test_database_upgrade():
    """测试数据库结构升级"""
    print("=" * 60)
    print("测试数据库结构升级")
    print("=" * 60)
    
    try:
        # 导入升级函数
        from value_averaging_strategy import upgrade_database_schema
        
        # 执行升级
        upgrade_database_schema()
        
        # 验证字段是否存在
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        cursor.execute("PRAGMA table_info(trade_orders)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'order_uuid' in columns:
            print("✅ order_uuid 字段已成功添加到 trade_orders 表")
        else:
            print("❌ order_uuid 字段未找到")
            return False
        
        # 检查索引是否存在
        cursor.execute("PRAGMA index_list(trade_orders)")
        indexes = cursor.fetchall()
        
        uuid_index_exists = any('uuid' in str(index) for index in indexes)
        if uuid_index_exists:
            print("✅ order_uuid 索引已创建")
        else:
            print("⚠️ order_uuid 索引未找到")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库升级测试失败：{str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_record_trade_order():
    """测试 record_trade_order 函数"""
    print("\n" + "=" * 60)
    print("测试 record_trade_order 函数")
    print("=" * 60)
    
    try:
        from value_averaging_strategy import record_trade_order
        
        test_uuid = f"test-record-{str(uuid.uuid4())}"
        
        # 测试记录订单
        order_id = record_trade_order(
            stock_code="159915.SZ",
            order_type="BUY",
            shares=1000,
            order_reason="TEST_RECORD",
            order_uuid=test_uuid
        )
        
        if order_id > 0:
            print(f"✅ 订单记录成功，ID: {order_id}")
            
            # 验证数据是否正确插入
            conn = sqlite3.connect(DATABASE_PATH)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT order_uuid, stock_code, order_type, target_shares, order_status
                FROM trade_orders 
                WHERE id = ?
            """, (order_id,))
            
            result = cursor.fetchone()
            if result:
                stored_uuid, stock_code, order_type, shares, status = result
                print(f"✅ 数据验证成功：")
                print(f"   UUID: {stored_uuid}")
                print(f"   股票: {stock_code}")
                print(f"   类型: {order_type}")
                print(f"   股数: {shares}")
                print(f"   状态: {status}")
                
                if stored_uuid == test_uuid:
                    print("✅ UUID 匹配正确")
                else:
                    print(f"❌ UUID 不匹配：期望 {test_uuid}，实际 {stored_uuid}")
            else:
                print("❌ 未找到插入的记录")
            
            # 清理测试数据
            cursor.execute("DELETE FROM trade_orders WHERE id = ?", (order_id,))
            conn.commit()
            conn.close()
            
            return True
        else:
            print("❌ 订单记录失败")
            return False
            
    except Exception as e:
        print(f"❌ record_trade_order 测试失败：{str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_order_error_callback():
    """测试 orderError_callback 函数"""
    print("\n" + "=" * 60)
    print("测试 orderError_callback 函数")
    print("=" * 60)
    
    try:
        # 创建测试数据
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        test_uuid = f"test-error-{str(uuid.uuid4())}"
        
        # 创建 trade_task_queue 记录
        cursor.execute("""
            INSERT INTO trade_task_queue
            (task_group_id, task_type, stock_code, target_shares, target_amount,
             estimated_price, estimated_fees, task_status, order_uuid, order_id, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (str(uuid.uuid4()), "BUY_159915_CASH", "159915.SZ", 1000, 2500.0,
              2.5, 5.0, "WAITING_CALLBACK", test_uuid, "TEST12345", current_time))
        
        task_id = cursor.lastrowid
        
        # 创建 trade_orders 记录（使用新的 order_uuid 字段）
        cursor.execute("""
            INSERT INTO trade_orders
            (order_date, stock_code, order_type, order_reason, target_shares,
             order_status, order_uuid, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (current_time, "159915.SZ", "BUY", "TEST_ERROR", 1000,
              "PENDING", test_uuid, current_time))
        
        order_id = cursor.lastrowid
        conn.commit()
        
        print(f"✅ 测试数据创建成功：task_id={task_id}, order_id={order_id}, uuid={test_uuid}")
        
        # 模拟 orderError_callback
        class MockOrderArgs:
            def __init__(self, strategy_name):
                self.strategyName = strategy_name
                self.stockCode = "159915.SZ"
                self.orderType = 23
                self.orderAmount = 1000
                self.orderPrice = 2.5
        
        mock_order_args = MockOrderArgs(f"价值平均策略_&&&_{test_uuid}")
        mock_error_msg = "测试错误：资金不足"
        
        # 导入并调用 orderError_callback
        from value_averaging_strategy import orderError_callback
        orderError_callback(None, mock_order_args, mock_error_msg)
        
        # 验证结果
        cursor.execute("""
            SELECT task_status, error_message FROM trade_task_queue 
            WHERE order_uuid = ?
        """, (test_uuid,))
        
        task_result = cursor.fetchone()
        if task_result:
            status, error = task_result
            if status == "FAILED":
                print("✅ trade_task_queue 状态更新成功")
            else:
                print(f"❌ trade_task_queue 状态错误：{status}")
        
        cursor.execute("""
            SELECT order_status, error_message FROM trade_orders 
            WHERE order_uuid = ?
        """, (test_uuid,))
        
        order_result = cursor.fetchone()
        if order_result:
            status, error = order_result
            if status == "FAILED":
                print("✅ trade_orders 状态更新成功")
            else:
                print(f"❌ trade_orders 状态错误：{status}")
        
        # 清理测试数据
        cursor.execute("DELETE FROM trade_task_queue WHERE order_uuid = ?", (test_uuid,))
        cursor.execute("DELETE FROM trade_orders WHERE order_uuid = ?", (test_uuid,))
        cursor.execute("DELETE FROM trade_task_log WHERE log_message LIKE '%测试错误%'")
        conn.commit()
        conn.close()
        
        print("✅ orderError_callback 测试完成")
        return True
        
    except Exception as e:
        print(f"❌ orderError_callback 测试失败：{str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_integration():
    """集成测试"""
    print("\n" + "=" * 60)
    print("集成测试总结")
    print("=" * 60)
    
    results = []
    
    # 1. 数据库升级测试
    results.append(("数据库结构升级", test_database_upgrade()))
    
    # 2. 订单记录测试
    results.append(("订单记录功能", test_record_trade_order()))
    
    # 3. 错误回调测试
    results.append(("错误回调处理", test_order_error_callback()))
    
    # 输出结果
    print("\n测试结果汇总：")
    print("-" * 40)
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if not passed:
            all_passed = False
    
    print("-" * 40)
    if all_passed:
        print("🎉 所有测试通过！order_uuid 集成成功！")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
    
    return all_passed

def main():
    print("order_uuid 字段集成测试")
    print("测试范围：数据库升级、订单记录、错误回调处理")
    
    try:
        test_integration()
    except Exception as e:
        print(f"测试执行失败：{str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
