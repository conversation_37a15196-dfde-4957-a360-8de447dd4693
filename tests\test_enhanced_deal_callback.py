# -*- coding: utf-8 -*-
"""
测试增强的成交回调处理功能
验证查询所有成交记录并汇总的功能
"""
import sys
import os
import sqlite3
import uuid
import datetime
from typing import Dict, List

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from value_averaging_strategy import (
    g_trade_task_callback_handler, g_db_connection, 
    ACCOUNT_ID, ACCOUNT_TYPE, TaskStatus
)

DATABASE_PATH = "trading_data.db"

class MockDealInfo:
    """模拟成交信息对象"""
    def __init__(self, shares: int, price: float, amount: float, commission: float = 5.0, 
                 order_uuid: str = "", order_id: str = "12345", stock_code: str = "159915.SZ"):
        self.m_nVolume = shares
        self.m_dPrice = price
        self.m_dTradeAmount = amount
        self.m_dCommission = commission
        self.m_strRemark = order_uuid
        self.m_strOrderSysID = order_id
        self.m_strInstrumentID = stock_code
        self.m_strTradeTime = datetime.datetime.now().strftime("%H:%M:%S")

def create_test_task(order_uuid: str, target_shares: int = 1000) -> int:
    """创建测试任务"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 插入测试任务
        cursor.execute("""
            INSERT INTO trade_task_queue
            (task_group_id, task_type, stock_code, target_shares, order_uuid, 
             task_status, created_time, updated_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            f"test-group-{uuid.uuid4()}",
            "BUY_CASH",
            "159915.SZ", 
            target_shares,
            order_uuid,
            TaskStatus.COMPLETED.value,  # 设置为已完成状态，这样成交回调才能找到
            current_time,
            current_time
        ))
        
        task_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        print(f"✅ 创建测试任务成功：ID={task_id}, UUID={order_uuid}")
        return task_id
        
    except Exception as e:
        print(f"❌ 创建测试任务失败：{str(e)}")
        return 0

def mock_get_trade_detail_data(account_id: str, account_type: str, data_type: str) -> List:
    """模拟 get_trade_detail_data 函数返回多条成交记录"""
    if data_type != 'DEAL':
        return []
    
    # 模拟分批成交的情况
    test_uuid = "test-enhanced-deal-callback"
    
    deals = [
        MockDealInfo(300, 2.45, 735.0, 5.0, test_uuid, "12345-1"),  # 第一笔成交
        MockDealInfo(400, 2.48, 992.0, 5.0, test_uuid, "12345-2"),  # 第二笔成交  
        MockDealInfo(300, 2.52, 756.0, 5.0, test_uuid, "12345-3"),  # 第三笔成交
    ]
    
    print(f"🔍 模拟返回{len(deals)}条成交记录")
    return deals

def test_enhanced_deal_callback():
    """测试增强的成交回调处理"""
    print("=" * 60)
    print("测试增强的成交回调处理功能")
    print("=" * 60)
    
    try:
        # 1. 创建测试订单UUID
        test_uuid = "test-enhanced-deal-callback"
        
        # 2. 创建测试任务
        task_id = create_test_task(test_uuid, 1000)
        if task_id == 0:
            return False
        
        # 3. 临时替换 get_trade_detail_data 函数
        import value_averaging_strategy
        original_func = getattr(value_averaging_strategy, 'get_trade_detail_data', None)
        value_averaging_strategy.get_trade_detail_data = mock_get_trade_detail_data
        
        # 4. 测试查询和汇总功能
        print(f"\n🔄 测试查询和汇总成交记录...")
        aggregated_data = g_trade_task_callback_handler.query_and_aggregate_deal_records(test_uuid)
        
        print(f"\n📊 汇总结果验证：")
        print(f"  总股数：{aggregated_data['total_shares']} (期望: 1000)")
        print(f"  总金额：{aggregated_data['total_amount']:.2f} (期望: 2483.00)")
        print(f"  成交均价：{aggregated_data['avg_price']:.4f} (期望: 2.4830)")
        print(f"  总手续费：{aggregated_data['total_commission']:.2f} (期望: 15.00)")
        
        # 5. 验证计算结果
        expected_total_shares = 300 + 400 + 300  # 1000
        expected_total_amount = 735.0 + 992.0 + 756.0  # 2483.0
        expected_avg_price = expected_total_amount / expected_total_shares  # 2.483
        expected_total_commission = 5.0 + 5.0 + 5.0  # 15.0
        
        success = True
        if aggregated_data['total_shares'] != expected_total_shares:
            print(f"❌ 总股数不匹配：实际{aggregated_data['total_shares']}, 期望{expected_total_shares}")
            success = False
            
        if abs(aggregated_data['total_amount'] - expected_total_amount) > 0.01:
            print(f"❌ 总金额不匹配：实际{aggregated_data['total_amount']:.2f}, 期望{expected_total_amount:.2f}")
            success = False
            
        if abs(aggregated_data['avg_price'] - expected_avg_price) > 0.0001:
            print(f"❌ 成交均价不匹配：实际{aggregated_data['avg_price']:.4f}, 期望{expected_avg_price:.4f}")
            success = False
            
        if abs(aggregated_data['total_commission'] - expected_total_commission) > 0.01:
            print(f"❌ 总手续费不匹配：实际{aggregated_data['total_commission']:.2f}, 期望{expected_total_commission:.2f}")
            success = False
        
        if success:
            print("✅ 汇总计算结果正确！")
        
        # 6. 测试完整的成交回调处理流程
        print(f"\n🔄 测试完整的成交回调处理流程...")
        
        # 查找任务
        task = g_trade_task_callback_handler.find_task_by_order_uuid(test_uuid, TaskStatus.COMPLETED.value)
        if task:
            print(f"✅ 找到测试任务：ID={task['id']}")
            
            # 模拟成交回调
            mock_deal_info = MockDealInfo(300, 2.45, 735.0, 5.0, test_uuid, "12345-1")
            g_trade_task_callback_handler.process_deal_callback_with_uuid(
                task, mock_deal_info, test_uuid, "12345-1"
            )
            
            print("✅ 成交回调处理完成")
        else:
            print("❌ 未找到测试任务")
            success = False
        
        # 7. 恢复原始函数
        if original_func:
            value_averaging_strategy.get_trade_detail_data = original_func
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败：{str(e)}")
        import traceback
        traceback.print_exc()
        return False

def check_database_records():
    """检查数据库记录"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        print(f"\n📊 检查数据库记录：")
        
        # 检查交易执行记录
        cursor.execute("SELECT COUNT(*) FROM trade_execution_log WHERE order_uuid LIKE 'test-enhanced%'")
        execution_count = cursor.fetchone()[0]
        print(f"  trade_execution_log: {execution_count} 条记录")
        
        # 检查费用明细记录
        cursor.execute("SELECT COUNT(*) FROM trade_fee_details WHERE order_uuid LIKE 'test-enhanced%'")
        fee_count = cursor.fetchone()[0]
        print(f"  trade_fee_details: {fee_count} 条记录")
        
        # 检查订单记录
        cursor.execute("SELECT COUNT(*) FROM trade_orders WHERE order_uuid LIKE 'test-enhanced%'")
        order_count = cursor.fetchone()[0]
        print(f"  trade_orders: {order_count} 条记录")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据库记录失败：{str(e)}")

if __name__ == "__main__":
    print("=" * 60)
    print("增强成交回调处理功能测试")
    print("=" * 60)
    
    if test_enhanced_deal_callback():
        print("\n✅ 测试成功！增强成交回调处理功能验证通过")
        check_database_records()
    else:
        print("\n❌ 测试失败！需要进一步检查")
    
    print("\n" + "=" * 60)
