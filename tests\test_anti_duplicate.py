#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试防重复交易机制
"""

import sqlite3
import datetime
import os

def test_anti_duplicate_mechanism():
    """测试防重复交易机制"""
    print("=== 测试防重复交易机制 ===")
    
    # 创建临时数据库
    db_path = "test_strategy.db"
    if os.path.exists(db_path):
        os.remove(db_path)
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 创建每日交易状态表
    cursor.execute("""
        CREATE TABLE daily_trade_status (
            trade_date TEXT PRIMARY KEY,
            buy_signal_executed INTEGER DEFAULT 0,
            sell_signal_executed INTEGER DEFAULT 0,
            value_avg_executed INTEGER DEFAULT 0,
            last_update_time TEXT,
            created_time TEXT NOT NULL
        )
    """)
    
    def is_trade_executed_today(trade_type: str, trade_date: str) -> bool:
        """检查当日是否已执行过指定类型的交易"""
        field_name = f"{trade_type}_executed"
        query = f"SELECT {field_name} FROM daily_trade_status WHERE trade_date = ?"
        cursor.execute(query, (trade_date,))
        result = cursor.fetchone()
        return result and result[0] == 1
    
    def mark_trade_executed(trade_type: str, trade_date: str):
        """标记当日指定类型的交易已执行"""
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        field_name = f"{trade_type}_executed"
        
        # 先尝试插入记录，如果已存在则更新
        cursor.execute("""
            INSERT OR IGNORE INTO daily_trade_status 
            (trade_date, created_time) VALUES (?, ?)
        """, (trade_date, current_time))
        
        # 更新执行状态
        update_query = f"""
            UPDATE daily_trade_status 
            SET {field_name} = 1, last_update_time = ? 
            WHERE trade_date = ?
        """
        cursor.execute(update_query, (current_time, trade_date))
        conn.commit()
    
    # 测试
    trade_date = "2024-01-15"
    
    # 测试初始状态
    if not is_trade_executed_today('buy_signal', trade_date):
        print("✓ 初始状态：买入信号未执行")
    else:
        print("✗ 初始状态异常：买入信号已标记为执行")
    
    # 标记买入信号已执行
    mark_trade_executed('buy_signal', trade_date)
    print("✓ 标记买入信号已执行")
    
    # 再次检查
    if is_trade_executed_today('buy_signal', trade_date):
        print("✓ 防重复机制工作正常：买入信号已执行")
    else:
        print("✗ 防重复机制失败：买入信号未正确标记")
    
    # 测试其他类型信号
    if not is_trade_executed_today('sell_signal', trade_date):
        print("✓ 卖出信号独立性正常：未受买入信号影响")
    else:
        print("✗ 信号独立性异常：卖出信号受买入信号影响")
    
    # 测试同一天重复标记
    mark_trade_executed('buy_signal', trade_date)
    if is_trade_executed_today('buy_signal', trade_date):
        print("✓ 重复标记测试通过：状态保持一致")
    
    # 测试不同日期
    trade_date2 = "2024-01-16"
    if not is_trade_executed_today('buy_signal', trade_date2):
        print("✓ 日期独立性正常：不同日期状态独立")
    else:
        print("✗ 日期独立性异常：不同日期状态相互影响")
    
    # 清理
    conn.close()
    os.remove(db_path)
    print("✓ 测试完成，数据库已清理")


def test_time_validation():
    """测试时间验证"""
    print("\n=== 测试时间验证 ===")
    
    def is_valid_check_time() -> bool:
        """检查当前时间是否在14:45之后"""
        current_time = datetime.datetime.now().time()
        return current_time >= datetime.time(14, 45)
    
    # 测试当前时间是否有效
    is_valid = is_valid_check_time()
    current_time = datetime.datetime.now().time()
    
    print(f"当前时间: {current_time}")
    print(f"是否为有效交易检测时间: {is_valid}")
    
    if current_time >= datetime.time(14, 45):
        if is_valid:
            print("✓ 时间验证正确：当前时间在14:45之后")
        else:
            print("✗ 时间验证错误：当前时间在14:45之后但返回False")
    else:
        if not is_valid:
            print("✓ 时间验证正确：当前时间在14:45之前")
        else:
            print("✗ 时间验证错误：当前时间在14:45之前但返回True")


def main():
    """主测试函数"""
    print("价值平均策略核心功能测试")
    print("=" * 50)
    
    test_anti_duplicate_mechanism()
    test_time_validation()
    
    print("\n" + "=" * 50)
    print("测试完成")


if __name__ == "__main__":
    main()
