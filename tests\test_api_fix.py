#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API修正的简单脚本
验证get_market_data_ex返回值处理是否正确
"""

from unittest.mock import Mock
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from value_averaging_strategy import update_technical_indicators, get_current_price, SIGNAL_FUND_CODE

def test_api_fix():
    """测试API修正"""
    print("开始测试API修正...")
    
    # 创建模拟的ContextInfo对象
    mock_context = Mock()
    
    # 模拟股票数据 - 符合技术文档的字典格式
    mock_stock_data = Mock()
    mock_stock_data.__len__ = Mock(return_value=100)
    
    # 模拟close价格数据
    close_values = Mock()
    close_values.values = [10.0 + i * 0.1 for i in range(100)]  # 递增的价格数据
    mock_stock_data.__getitem__ = Mock(side_effect=lambda key: {
        'close': close_values,
        'high': close_values,
        'open': close_values,
        'low': close_values
    }[key])
    
    # 模拟iloc访问
    mock_iloc = Mock()
    mock_iloc.__getitem__ = Mock(return_value=19.9)  # 最新价格
    mock_stock_data.iloc = mock_iloc
    
    # 根据技术文档，get_market_data_ex返回字典格式 {code: data}
    mock_market_data = {
        SIGNAL_FUND_CODE: mock_stock_data
    }
    
    mock_context.get_market_data_ex.return_value = mock_market_data
    
    try:
        # 测试技术指标更新
        print("测试技术指标更新...")
        result = update_technical_indicators(mock_context)
        if result is not None:
            print("✓ 技术指标更新测试通过")
            print(f"  EMA值: {result.get('ema_value', 'N/A')}")
            print(f"  当前收盘价: {result.get('current_close', 'N/A')}")
        else:
            print("✗ 技术指标更新测试失败")
            
        # 测试价格获取
        print("测试价格获取...")
        price = get_current_price(SIGNAL_FUND_CODE, mock_context)
        if price > 0:
            print(f"✓ 价格获取测试通过，价格: {price}")
        else:
            print("✗ 价格获取测试失败")
            
        print("API修正测试完成")
        
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_api_fix()
