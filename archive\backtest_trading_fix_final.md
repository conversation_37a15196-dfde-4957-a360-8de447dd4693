# 回测模式交易修复 - 最终版本

## 问题分析

用户发现回测模式下存在以下问题：

1. **持仓数据丢失**：执行阶段切换后只有日志记录，没有保存模拟持仓数据
2. **无法模拟卖出**：后续阶段切换时找不到持仓，无法执行卖出操作
3. **交易方式错误**：没有使用iQuant的passorder函数，无法在平台看到策略收益
4. **买入逻辑错误**：使用全仓买入而非价值平均策略计算

## 用户需求

1. **真实模拟交易**：不只是日志记录，要像实盘一样记录持仓等关键信息
2. **100%成功交易**：回测模式下假设所有交易都成功
3. **使用passorder**：让iQuant平台能够跟踪策略收益情况
4. **正确持仓管理**：确保后续阶段切换能找到持仓进行卖出
5. **价值平均策略**：按期数计算目标金额，而非全仓买入

## 修复实现

### 1. 修改交易执行函数

```python
def execute_trade_order(stock_code: str, order_type: str, shares: int, order_reason: str, ContextInfo) -> bool:
    """执行交易指令（回测适配）"""
    # 检查是否为回测模式
    if is_backtest_mode(ContextInfo):
        # 回测模式：使用passorder进行真实模拟交易
        success = execute_backtest_trade(stock_code, order_type, shares, ContextInfo)
        if success:
            update_trade_order_status(order_id, 'SUCCESS', shares, current_price)
        return success
    else:
        # 实盘模式：执行真实交易
        # ... 原有实盘交易逻辑
```

### 2. 创建回测交易函数

```python
def execute_backtest_trade(stock_code: str, order_type: str, shares: int, ContextInfo) -> bool:
    """执行回测模式下的真实模拟交易"""
    # 设置ContextInfo属性
    ContextInfo.stock = stock_code
    if not hasattr(ContextInfo, 'accountid'):
        ContextInfo.accountid = ACCOUNT_ID
    
    if order_type == 'BUY':
        # 回测买入：passorder(23, 1101, C.accountid, C.stock, 5, -1, vol, C)
        result = passorder(23, 1101, ContextInfo.accountid, ContextInfo.stock, 5, -1, shares, ContextInfo)
    elif order_type == 'SELL':
        # 回测卖出：passorder(24, 1101, C.accountid, C.stock, 5, -1, holding_vol, C)
        result = passorder(24, 1101, ContextInfo.accountid, ContextInfo.stock, 5, -1, shares, ContextInfo)
    
    # 记录持仓变化到数据库
    record_position_change(stock_code, order_type, shares, ContextInfo)
    
    return True  # 回测模式假设100%成功
```

### 3. 创建持仓管理函数

```python
def record_position_change(stock_code: str, order_type: str, shares: int, ContextInfo):
    """记录持仓变化到数据库（回测模式专用）"""
    # 获取当前持仓
    current_position = get_current_position(stock_code)
    current_shares = current_position.get('shares', 0)
    current_avg_cost = current_position.get('avg_cost', 0)
    current_price = get_current_price(stock_code, ContextInfo)

    if order_type == 'BUY':
        # 买入：增加持仓，计算新平均成本
        new_shares = current_shares + shares
        if current_shares > 0:
            total_cost = current_shares * current_avg_cost + shares * current_price
            new_avg_cost = total_cost / new_shares
        else:
            new_avg_cost = current_price
    elif order_type == 'SELL':
        # 卖出：减少持仓
        new_shares = max(0, current_shares - shares)
        new_avg_cost = current_avg_cost

    # 保存到数据库
    new_market_value = new_shares * current_price
    cursor.execute("INSERT INTO position_records (...) VALUES (...)")
```

### 4. 修改阶段切换逻辑

```python
# sleeping -> active: 按价值平均策略买入
current_period = calculate_current_period(g_strategy_status['start_period_date'], ContextInfo)
target_amount = current_period * PERIOD_INVESTMENT_AMOUNT
current_price = get_current_price(ACTIVE_FUND_CODE, ContextInfo)
shares_to_buy = int(target_amount / current_price / MIN_TRADE_SHARES) * MIN_TRADE_SHARES

# active -> sleeping: 卖出所有159915持仓
position_159915 = get_current_position(ACTIVE_FUND_CODE)
if position_159915['shares'] > 0:
    execute_trade_order(ACTIVE_FUND_CODE, 'SELL', position_159915['shares'], 'SIGNAL_SELL', ContextInfo)
```

### 5. 增强价格获取

```python
def get_current_price(stock_code: str, ContextInfo) -> float:
    """获取股票当前价格（回测适配）"""
    if is_backtest_mode(ContextInfo):
        return get_backtest_current_price(stock_code, ContextInfo)  # 当前K线收盘价
    else:
        return get_realtime_current_price(stock_code, ContextInfo)  # 最新价格
```

## 修复效果

### ✅ 解决的问题

1. **持仓数据保存**：每次交易后都会更新position_records表
2. **模拟卖出支持**：后续阶段切换能正确查询到持仓并执行卖出
3. **iQuant收益跟踪**：使用passorder让平台能够跟踪策略收益
4. **价值平均策略**：正确按期数计算目标金额和买入股数

### ✅ 关键改进

1. **真实模拟交易**
   - 使用passorder而非仅日志记录
   - 100%交易成功率（回测假设）
   - iQuant平台能够跟踪收益

2. **完整持仓管理**
   - 数据库记录每次持仓变化
   - 支持平均成本计算
   - 支持多次交易的持仓累积

3. **回测模式适配**
   - 价格获取使用当前K线收盘价
   - 时间计算使用当前K线时间
   - 完整的阶段切换支持

4. **价值平均策略**
   - 正确的期数计算
   - 目标金额 = 期数 × 每期投入金额
   - 精确的股数计算（按最小交易单位调整）

## 使用说明

### 回测模式下的交易流程

1. **阶段切换触发**：策略检测到信号，需要执行阶段切换
2. **价值平均计算**：根据当前期数计算目标投入金额
3. **passorder调用**：使用iQuant的passorder函数执行交易
4. **持仓更新**：将交易结果记录到position_records表
5. **收益跟踪**：iQuant平台自动跟踪策略收益

### 关键参数

- `PERIOD_INVESTMENT_AMOUNT`：每期投入金额
- `INVESTMENT_CYCLE`：投资周期（支持"1mon"、"1q"等）
- `MIN_TRADE_SHARES`：最小交易股数
- `IS_BACKTEST_MODE`：回测模式开关

### 监控要点

- 观察passorder调用日志
- 检查持仓记录更新
- 验证期数计算准确性
- 确认价格获取正确性

## 总结

修复后的策略在回测模式下能够：

1. ✅ **正确执行价值平均策略**：按期数计算目标金额，精确买入
2. ✅ **使用passorder进行交易**：让iQuant平台跟踪策略收益
3. ✅ **完整的持仓管理**：每次交易后正确更新持仓记录
4. ✅ **支持完整阶段切换**：sleeping ↔ active 双向切换都能正常工作
5. ✅ **回测模式适配**：价格、时间、交易都适配回测环境

现在您的策略应该能够在回测模式下正常工作，不再出现持仓数据丢失的问题，并且iQuant平台能够正确显示策略收益情况。
