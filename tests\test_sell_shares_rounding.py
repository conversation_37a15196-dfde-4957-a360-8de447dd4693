#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试卖出股数整数倍要求
验证卖出逻辑是否确保股数是MIN_TRADE_SHARES的整数倍
"""

MIN_TRADE_SHARES = 100  # 1手 = 100股

def test_value_averaging_sell_calculation():
    """测试价值平均策略中的卖出计算"""
    print("=== 测试价值平均策略卖出计算 ===")
    
    test_cases = [
        # (需要卖出金额, 当前价格, 预期股数, 测试描述)
        (23280, 80, 200, "需要卖出23280元，价格80元，291股 -> 200股"),
        (15000, 50, 300, "需要卖出15000元，价格50元，300股 -> 300股"),
        (12500, 50, 200, "需要卖出12500元，价格50元，250股 -> 200股"),
        (5500, 50, 100, "需要卖出5500元，价格50元，110股 -> 100股"),
        (4500, 50, 100, "需要卖出4500元，价格50元，90股 -> 100股（最少100股）"),
        (500, 50, 100, "需要卖出500元，价格50元，10股 -> 100股（最少100股）"),
    ]
    
    passed = 0
    failed = 0
    
    for sell_amount, current_price, expected, description in test_cases:
        # 模拟价值平均策略中的卖出计算逻辑
        ideal_shares = int(sell_amount / current_price)
        trade_shares = max(MIN_TRADE_SHARES, int(ideal_shares / MIN_TRADE_SHARES) * MIN_TRADE_SHARES)
        
        if trade_shares == expected:
            print(f"✅ {description}")
            print(f"   计算结果：{trade_shares}股")
            passed += 1
        else:
            print(f"❌ {description}")
            print(f"   预期：{expected}股，实际：{trade_shares}股")
            failed += 1
        
        # 验证结果是否是MIN_TRADE_SHARES的倍数
        if trade_shares % MIN_TRADE_SHARES != 0:
            print(f"   ⚠️ 结果不是{MIN_TRADE_SHARES}的倍数！")
            failed += 1
        else:
            print(f"   ✓ 结果是{MIN_TRADE_SHARES}的倍数")
        print()
    
    print(f"价值平均卖出测试：✅ {passed} 个通过，❌ {failed} 个失败")
    return failed == 0

def test_510720_partial_sell_calculation():
    """测试510720部分卖出计算"""
    print("\n=== 测试510720部分卖出计算 ===")
    
    # 模拟费率
    COMMISSION_FEE_RATE = 0.0003
    SELL_TAX_RATE = 0.001
    
    test_cases = [
        # (总需求金额, 510720价格, 预期股数, 测试描述)
        (10000, 1.0, 10100, "需要10000元，价格1.0元，考虑费用后需要10100股 -> 10100股"),
        (5000, 1.2, 4200, "需要5000元，价格1.2元，考虑费用后需要4167股 -> 4200股"),
        (3000, 0.8, 3800, "需要3000元，价格0.8元，考虑费用后需要3754股 -> 3800股"),
        (1500, 1.5, 1100, "需要1500元，价格1.5元，考虑费用后需要1001股 -> 1100股"),
    ]
    
    passed = 0
    failed = 0
    
    for total_needed, current_price_510720, expected, description in test_cases:
        # 模拟510720部分卖出计算逻辑
        needed_sell_amount = total_needed / (1 - COMMISSION_FEE_RATE - SELL_TAX_RATE)
        ideal_shares = int(needed_sell_amount / current_price_510720) + 1
        shares_to_sell_rounded = int((ideal_shares + MIN_TRADE_SHARES - 1) / MIN_TRADE_SHARES) * MIN_TRADE_SHARES
        
        if shares_to_sell_rounded == expected:
            print(f"✅ {description}")
            print(f"   计算过程：需要{needed_sell_amount:.2f}元 -> {ideal_shares}股 -> {shares_to_sell_rounded}股")
            passed += 1
        else:
            print(f"❌ {description}")
            print(f"   预期：{expected}股，实际：{shares_to_sell_rounded}股")
            print(f"   计算过程：需要{needed_sell_amount:.2f}元 -> {ideal_shares}股 -> {shares_to_sell_rounded}股")
            failed += 1
        
        # 验证结果是否是MIN_TRADE_SHARES的倍数
        if shares_to_sell_rounded % MIN_TRADE_SHARES != 0:
            print(f"   ⚠️ 结果不是{MIN_TRADE_SHARES}的倍数！")
            failed += 1
        else:
            print(f"   ✓ 结果是{MIN_TRADE_SHARES}的倍数")
        print()
    
    print(f"510720部分卖出测试：✅ {passed} 个通过，❌ {failed} 个失败")
    return failed == 0

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    edge_cases = [
        # 价值平均策略边界情况
        ("价值平均", 99, 1.0, 100, "理想股数99 -> 100股（最少100股）"),
        ("价值平均", 150, 1.0, 100, "理想股数150 -> 100股（向下取整到100的倍数）"),
        ("价值平均", 199, 1.0, 100, "理想股数199 -> 100股（向下取整到100的倍数）"),
        ("价值平均", 200, 1.0, 200, "理想股数200 -> 200股（正好是100的倍数）"),
        ("价值平均", 250, 1.0, 200, "理想股数250 -> 200股（向下取整到100的倍数）"),
        
        # 510720部分卖出边界情况
        ("510720", 99, 1.0, 100, "理想股数99+1=100 -> 100股"),
        ("510720", 150, 1.0, 200, "理想股数150+1=151 -> 200股（向上取整到100的倍数）"),
        ("510720", 199, 1.0, 200, "理想股数199+1=200 -> 200股"),
        ("510720", 250, 1.0, 300, "理想股数250+1=251 -> 300股（向上取整到100的倍数）"),
    ]
    
    passed = 0
    failed = 0
    
    for test_type, ideal_shares, price, expected, description in edge_cases:
        if test_type == "价值平均":
            # 价值平均策略逻辑
            trade_shares = max(MIN_TRADE_SHARES, int(ideal_shares / MIN_TRADE_SHARES) * MIN_TRADE_SHARES)
        else:  # 510720
            # 510720部分卖出逻辑
            ideal_shares_plus_one = ideal_shares + 1
            trade_shares = int((ideal_shares_plus_one + MIN_TRADE_SHARES - 1) / MIN_TRADE_SHARES) * MIN_TRADE_SHARES
        
        if trade_shares == expected:
            print(f"✅ {description}")
            passed += 1
        else:
            print(f"❌ {description}")
            print(f"   预期：{expected}股，实际：{trade_shares}股")
            failed += 1
        
        # 验证结果是否是MIN_TRADE_SHARES的倍数
        if trade_shares % MIN_TRADE_SHARES != 0:
            print(f"   ⚠️ 结果不是{MIN_TRADE_SHARES}的倍数！")
            failed += 1
    
    print(f"\n边界测试：✅ {passed} 个通过，❌ {failed} 个失败")
    return failed == 0

def main():
    """主测试函数"""
    print("开始测试卖出股数整数倍要求...")
    print("=" * 50)
    
    all_passed = True
    
    # 测试价值平均策略卖出计算
    if not test_value_averaging_sell_calculation():
        all_passed = False
    
    # 测试510720部分卖出计算
    if not test_510720_partial_sell_calculation():
        all_passed = False
    
    # 测试边界情况
    if not test_edge_cases():
        all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("🎉 所有测试通过！卖出股数整数倍要求实现正确。")
        print("\n📋 修改总结：")
        print("✅ 价值平均策略：卖出股数向下取整到100的倍数，最少100股")
        print("✅ 510720部分卖出：卖出股数向上取整到100的倍数")
        print("✅ 所有卖出操作都确保股数是100的整数倍")
    else:
        print("❌ 部分测试失败，需要修复实现。")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
