# -*- coding: utf-8 -*-
"""
测试修复后的重采样函数
"""

import pandas as pd
import numpy as np
import sys
import os

# 添加当前目录到路径，以便导入value_averaging_strategy模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_resample_with_mock_data():
    """使用模拟数据测试重采样函数"""
    print("=== 测试修复后的重采样函数 ===")
    
    # 模拟iQuant API返回的数据格式
    # 创建一年的日线数据
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    n_days = len(dates)
    
    # 模拟价格数据
    base_price = 10.0
    daily_data = pd.DataFrame({
        'open': base_price + np.random.randn(n_days) * 0.1,
        'high': base_price + np.random.randn(n_days) * 0.1 + 0.2,
        'low': base_price + np.random.randn(n_days) * 0.1 - 0.2,
        'close': base_price + np.random.randn(n_days) * 0.1,
        'volume': np.random.randint(1000, 10000, n_days),
        'amount': np.random.randint(10000, 100000, n_days)
    }, index=dates)
    
    print(f"原始日线数据: {len(daily_data)} 条记录")
    print(f"日期范围: {daily_data.index[0]} 到 {daily_data.index[-1]}")
    print(f"数据列: {list(daily_data.columns)}")
    
    # 定义简化版的重采样函数（复制主要逻辑）
    def resample_daily_to_period_test(daily_data, period_type='1q'):
        import pandas as pd
        
        print(f"[重采样调试] 开始重采样，目标周期: {period_type}")
        
        try:
            if daily_data is None:
                print("[重采样调试] 输入数据为None")
                return None
                
            print(f"[重采样调试] 输入数据类型: {type(daily_data)}")
            print(f"[重采样调试] 输入数据形状: {daily_data.shape}")
                
            if not isinstance(daily_data, pd.DataFrame):
                print(f"[重采样调试] 数据不是DataFrame")
                return daily_data
            
            if len(daily_data) == 0:
                print("[重采样调试] 数据为空")
                return daily_data
                
            print(f"[重采样调试] 数据列: {list(daily_data.columns)}")
            print(f"[重采样调试] 当前索引类型: {type(daily_data.index)}")
                
            if not isinstance(daily_data.index, pd.DatetimeIndex):
                print("[重采样调试] 检测到非时间索引，创建时间索引...")
                end_date = pd.Timestamp.now().normalize()
                start_date = end_date - pd.Timedelta(days=len(daily_data)-1)
                new_index = pd.date_range(start=start_date, end=end_date, periods=len(daily_data))
                daily_data.index = new_index
                print(f"[重采样调试] 已创建时间索引: {daily_data.index[0]} 到 {daily_data.index[-1]}")
                
            if period_type == '1q':
                rule = 'Q'
            elif period_type == '1mon':
                rule = 'M'
            else:
                print(f"[重采样调试] 不支持的周期类型: {period_type}")
                return daily_data
            
            print(f"[重采样调试] 使用重采样规则: {rule}")
            
            agg_rules = {}
            for col in daily_data.columns:
                if col in ['open']:
                    agg_rules[col] = 'first'
                elif col in ['high']:
                    agg_rules[col] = 'max'
                elif col in ['low']:
                    agg_rules[col] = 'min'
                elif col in ['close']:
                    agg_rules[col] = 'last'
                elif col in ['volume', 'amount']:
                    agg_rules[col] = 'sum'
                else:
                    agg_rules[col] = 'last'
            
            print(f"[重采样调试] 聚合规则: {agg_rules}")
            
            print("[重采样调试] 开始执行重采样...")
            resampled = daily_data.resample(rule).agg(agg_rules).dropna()
            
            print(f"[重采样调试] 重采样完成！")
            print(f"[重采样调试] 原始数据: {len(daily_data)} 行")
            print(f"[重采样调试] 重采样后: {len(resampled)} 行")
            print(f"[重采样调试] 重采样后索引: {resampled.index.tolist()}")
            
            if len(resampled) == 0:
                print("[重采样调试] 警告：重采样后数据为空！")
                return None
            
            return resampled
            
        except Exception as e:
            print(f"[重采样调试] 错误: {str(e)}")
            raise Exception(f"数据重采样失败: {str(e)}")
    
    # 测试季线重采样
    print(f"\n=== 测试季线重采样 ===")
    try:
        quarterly_result = resample_daily_to_period_test(daily_data, '1q')
        if quarterly_result is not None:
            print(f"✅ 季线重采样成功!")
            print(f"   原始数据: {len(daily_data)} 条日线")
            print(f"   重采样后: {len(quarterly_result)} 条季线")
            print(f"   季线数据预览:")
            print(quarterly_result)
        else:
            print("❌ 季线重采样返回None")
    except Exception as e:
        print(f"❌ 季线重采样失败: {str(e)}")
    
    # 测试月线重采样
    print(f"\n=== 测试月线重采样 ===")
    try:
        monthly_result = resample_daily_to_period_test(daily_data, '1mon')
        if monthly_result is not None:
            print(f"✅ 月线重采样成功!")
            print(f"   原始数据: {len(daily_data)} 条日线")
            print(f"   重采样后: {len(monthly_result)} 条月线")
            print(f"   月线数据预览:")
            print(monthly_result.head())
        else:
            print("❌ 月线重采样返回None")
    except Exception as e:
        print(f"❌ 月线重采样失败: {str(e)}")

def test_edge_cases():
    """测试边界情况"""
    print(f"\n=== 测试边界情况 ===")
    
    # 测试空数据
    print("测试空数据...")
    empty_df = pd.DataFrame()
    # 这里应该返回空DataFrame而不是抛出异常
    
    # 测试没有时间索引的数据
    print("测试没有时间索引的数据...")
    data_no_index = pd.DataFrame({
        'open': [10, 11, 12],
        'high': [10.5, 11.5, 12.5],
        'low': [9.5, 10.5, 11.5],
        'close': [10.2, 11.2, 12.2]
    })
    print(f"无时间索引数据: {data_no_index.shape}, 索引类型: {type(data_no_index.index)}")

if __name__ == "__main__":
    test_resample_with_mock_data()
    test_edge_cases()
    print("\n测试完成")
