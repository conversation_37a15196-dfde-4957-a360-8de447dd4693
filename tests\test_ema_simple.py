# -*- coding: utf-8 -*-

"""
简单的 EMA 计算测试
验证策略中的 EMA 计算是否正常工作
"""

import numpy as np

def calculate_ema_fallback(prices, period):
    """EMA 计算的备用算法"""
    if len(prices) < period:
        return []

    ema_values = []
    multiplier = 2.0 / (period + 1)

    # 第一个EMA值使用简单移动平均
    sma = sum(prices[:period]) / period
    ema_values.append(sma)

    # 计算后续的EMA值
    for i in range(period, len(prices)):
        ema = (prices[i] * multiplier) + (ema_values[-1] * (1 - multiplier))
        ema_values.append(ema)

    return ema_values

def test_ema_calculation():
    """
    测试 EMA 计算功能
    """
    print("=== EMA 计算测试 ===")
    
    # 测试数据
    test_prices = [10.0, 11.0, 12.0, 11.5, 13.0, 12.8, 14.0, 13.5, 15.0, 14.2, 16.0, 15.5]
    period = 5
    
    print(f"测试数据: {test_prices}")
    print(f"EMA 周期: {period}")
    
    # 使用备用算法计算
    ema_result = calculate_ema_fallback(test_prices, period)
    print(f"EMA 计算结果: {ema_result}")
    
    # 验证结果
    if len(ema_result) == len(test_prices) - period + 1:
        print("✓ EMA 计算结果长度正确")
    else:
        print(f"✗ EMA 计算结果长度错误，期望 {len(test_prices) - period + 1}，实际 {len(ema_result)}")
    
    # 验证第一个值是简单移动平均
    expected_first_sma = sum(test_prices[:period]) / period
    if abs(ema_result[0] - expected_first_sma) < 1e-10:
        print("✓ 第一个 EMA 值（SMA）计算正确")
    else:
        print(f"✗ 第一个 EMA 值错误，期望 {expected_first_sma}，实际 {ema_result[0]}")
    
    # 验证 EMA 递增趋势（因为测试数据总体上升）
    if ema_result[-1] > ema_result[0]:
        print("✓ EMA 趋势符合预期（上升）")
    else:
        print("✗ EMA 趋势不符合预期")
    
    print("✓ EMA 计算功能正常")
    return True

if __name__ == "__main__":
    test_ema_calculation()
