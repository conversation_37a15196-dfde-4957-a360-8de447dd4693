# 交易费用跟踪升级总结

## 升级目标

在成交回调中详细记录交易的均价（m_dPrice）、手续费（m_dCommission）、印花税、过户费等所有费用信息，提供完整的交易成本分析。

## 主要升级内容

### 1. 数据库结构升级

#### 新增费用明细表
```sql
CREATE TABLE IF NOT EXISTS trade_fee_details (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    execution_log_id INTEGER,            -- 关联trade_execution_log.id
    order_uuid TEXT NOT NULL,            -- 订单UUID
    commission REAL DEFAULT 0,           -- 佣金
    stamp_tax REAL DEFAULT 0,            -- 印花税
    transfer_fee REAL DEFAULT 0,         -- 过户费
    other_fees REAL DEFAULT 0,           -- 其他费用
    total_fees REAL DEFAULT 0,           -- 总费用
    net_amount REAL DEFAULT 0,           -- 净金额
    created_time TEXT NOT NULL,          -- 创建时间
    FOREIGN KEY (execution_log_id) REFERENCES trade_execution_log (id)
);
```

#### 新增索引
```sql
CREATE INDEX IF NOT EXISTS idx_trade_fee_details_uuid ON trade_fee_details(order_uuid);
CREATE INDEX IF NOT EXISTS idx_trade_fee_details_execution_id ON trade_fee_details(execution_log_id);
```

### 2. 成交回调函数升级

#### 增强的费用信息获取
```python
def deal_callback(ContextInfo, dealInfo):
    # 获取基本成交信息
    deal_shares = 成交量
    deal_price = 成交均价
    deal_amount = 成交金额
    
    # 获取详细费用信息
    deal_commission = 佣金  # m_dCommission, m_dComssion, m_dFee, m_dTradeFee
    stamp_tax = 印花税      # m_dStampTax, m_dTax, m_dStampDuty
    transfer_fee = 过户费   # m_dTransferFee, m_dTransFee, m_dSettleFee
    
    # 计算总费用和净金额
    total_fees = deal_commission + stamp_tax + transfer_fee
    net_amount = deal_amount - total_fees
```

#### 多属性名尝试机制
```python
# 成交价（均价）
for attr in ['m_dPrice', 'm_dDealPrice', 'm_dTradePrice', 'm_dAvgPrice']:
    # 尝试获取有效值

# 手续费
for attr in ['m_dCommission', 'm_dComssion', 'm_dFee', 'm_dTradeFee']:
    # 尝试获取有效值

# 印花税
for attr in ['m_dStampTax', 'm_dTax', 'm_dStampDuty']:
    # 尝试获取有效值

# 过户费
for attr in ['m_dTransferFee', 'm_dTransFee', 'm_dSettleFee']:
    # 尝试获取有效值
```

### 3. 详细的日志输出

#### 成交信息显示
```python
print(f"收到成交回调：UUID={order_uuid}，订单ID={order_id}，股票={instrument_id}")
print(f"  成交详情：{deal_shares}股 × {deal_price:.4f}元 = {deal_amount:.2f}元")
print(f"  费用明细：佣金{deal_commission:.2f}元")
if stamp_tax > 0:
    print(f"           印花税{stamp_tax:.2f}元")
if transfer_fee > 0:
    print(f"           过户费{transfer_fee:.2f}元")
print(f"  总费用：{total_fees:.2f}元，净额：{net_amount:.2f}元，时间：{trade_time}")
```

### 4. 数据库记录升级

#### 双表记录机制
```python
# 更新主执行记录表
UPDATE trade_execution_log 
SET order_id = ?, price = ?, amount = ?, fees = ?, status = 'SUCCESS'
WHERE order_uuid = ? AND status = 'PENDING'

# 插入详细费用记录
INSERT INTO trade_fee_details
(execution_log_id, order_uuid, commission, stamp_tax, transfer_fee, 
 other_fees, total_fees, net_amount, created_time)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
```

### 5. 费用统计功能

#### 费用统计摘要
```python
def get_trading_fee_summary():
    """获取交易费用统计摘要"""
    # 查询总交易次数、总佣金、总印花税、总过户费等
    # 计算平均每笔费用
    # 显示净交易额
```

#### 最近交易记录
```python
def print_recent_trades(limit: int = 5):
    """打印最近的交易记录"""
    # 显示最近N笔交易的详细信息
    # 包含费用明细和净金额
```

### 6. 启动时显示统计

在策略初始化时自动显示：
- 交易费用统计摘要
- 最近3笔交易记录

## 预期运行效果

### 成交回调输出
```
🔍 成交对象所有属性：
   m_strOrderSysID = *********
   m_dPrice = 2.4500
   m_dCommission = 5.00
   m_dStampTax = 2.45
   m_dTransferFee = 0.25
   ...

✓ 找到成交价属性：m_dPrice = 2.4500
✓ 找到手续费属性：m_dCommission = 5.00
✓ 找到印花税属性：m_dStampTax = 2.45
✓ 找到过户费属性：m_dTransferFee = 0.25

收到成交回调：UUID=abc-123，订单ID=*********，股票=159915.SZ
  成交详情：100股 × 2.4500元 = 245.00元
  费用明细：佣金5.00元
           印花税2.45元
           过户费0.25元
  总费用：7.70元，净额：237.30元，时间：14:30:25

✓ 交易执行记录已更新：UUID=abc-123，订单ID=*********，记录ID=1
  记录详情：均价2.4500，金额245.00，总费用7.70，净额237.30
✓ 费用明细已记录：佣金5.00，印花税2.45，过户费0.25
```

### 启动时统计显示
```
📊 交易费用统计摘要：
  总交易次数：5
  总佣金：25.00元
  总印花税：12.25元
  总过户费：1.25元
  总费用：38.50元
  净交易额：1,211.50元
  平均每笔费用：7.70元

📈 最近3笔交易记录：
================================================================================
2025-08-14 14:30:25 | 卖出 159915.SZ
  数量：100股 × 2.4500元 = 245.00元
  费用：佣金5.00 + 印花税2.45 + 过户费0.25 = 7.70元
  净额：237.30元
--------------------------------------------------------------------------------
2025-08-14 09:30:15 | 买入 159915.SZ
  数量：100股 × 2.4200元 = 242.00元
  费用：佣金5.00 + 印花税0.00 + 过户费0.24 = 5.24元
  净额：-247.24元
--------------------------------------------------------------------------------
```

## 数据分析优势

### 1. 完整的成本跟踪
- ✅ 详细记录所有费用类型
- ✅ 区分佣金、印花税、过户费
- ✅ 计算真实的净收益

### 2. 精确的盈亏计算
- ✅ 考虑所有交易成本
- ✅ 提供净金额数据
- ✅ 支持费用统计分析

### 3. 交易成本优化
- ✅ 监控平均交易费用
- ✅ 识别高费用交易
- ✅ 支持费用率分析

### 4. 历史数据分析
- ✅ 完整的费用历史记录
- ✅ 支持时间段统计
- ✅ 便于策略优化

## 技术特点

### 1. 容错性强
- ✅ 多属性名尝试机制
- ✅ 兼容不同版本的iQuant
- ✅ 详细的调试信息

### 2. 数据完整性
- ✅ 双表存储机制
- ✅ 外键关联保证一致性
- ✅ 索引优化查询性能

### 3. 实时监控
- ✅ 即时显示费用信息
- ✅ 自动计算净收益
- ✅ 详细的日志记录

这个升级使策略能够精确跟踪所有交易成本，为量化交易提供完整的成本分析基础。
