#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test get_current_time function fix
"""

import datetime

# Mock global variables
g_current_bar_time = None
IS_BACKTEST_MODE = False

def is_backtest_mode(ContextInfo):
    """Mock backtest mode check"""
    return IS_BACKTEST_MODE

def log_message(level, operation, message, details, ContextInfo):
    """Mock log function"""
    print(f"[{level}] {operation}: {message}")

def get_current_time(ContextInfo):
    """
    Get current time (fixed version)
    """
    global g_current_bar_time

    try:
        if is_backtest_mode(ContextInfo):
            # Backtest mode: use current K-line time
            try:
                current_bar_timestamp = ContextInfo.get_bar_timetag(ContextInfo.barpos)
                if current_bar_timestamp:
                    print(f'current_bar_ts ===> {current_bar_timestamp}')
                    g_current_bar_time = datetime.datetime.fromtimestamp(current_bar_timestamp/1000)
            except:
                print('Failed to get K-line time, will return global bartime')
                pass
            
            print(f'barpos = {ContextInfo.barpos}, bartime = {g_current_bar_time}')
            # If unable to get K-line time, use cached time
            if g_current_bar_time:
                return g_current_bar_time
            else:
                # Cannot get k-line time, no cached time, return current
                return datetime.datetime.now()
        else:
            # Live mode or unable to get K-line time: use system current time
            current_time = datetime.datetime.now()
            # In live mode, also need to set g_current_bar_time for other functions to use
            g_current_bar_time = current_time
            return current_time

    except Exception as e:
        log_message("WARNING", "time_get", f"Failed to get current time: {str(e)}", None, ContextInfo)
        current_time = datetime.datetime.now()
        # In exception case, also need to set g_current_bar_time
        g_current_bar_time = current_time
        return current_time

# Mock ContextInfo
class MockContextInfo:
    def __init__(self):
        self.barpos = 100
    
    def get_bar_timetag(self, barpos):
        return None

def test_fix():
    """
    Test the fix
    """
    print("=" * 50)
    print("Test get_current_time fix")
    print("=" * 50)
    
    # Test live mode
    print("1. Test live mode:")
    mock_context = MockContextInfo()
    
    print(f"Before call g_current_bar_time: {g_current_bar_time}")
    
    current_time = get_current_time(mock_context)
    print(f"get_current_time returned: {current_time}")
    print(f"After call g_current_bar_time: {g_current_bar_time}")
    
    if g_current_bar_time is not None:
        print("✅ g_current_bar_time is correctly set in live mode")
        
        # Test using g_current_bar_time in update_technical_indicators
        try:
            time_str = g_current_bar_time.strftime('%Y%m%d')
            print(f"✅ g_current_bar_time.strftime('%Y%m%d') = {time_str}")
            print("✅ Fix successful! Now can safely use g_current_bar_time in update_technical_indicators")
        except Exception as e:
            print(f"❌ Failed to use g_current_bar_time.strftime: {str(e)}")
    else:
        print("❌ g_current_bar_time not set in live mode")
    
    # Test exception case
    print("\n2. Test exception case:")
    class ErrorContextInfo:
        def __init__(self):
            self.barpos = 100
        
        def get_bar_timetag(self, barpos):
            raise Exception("Mock exception")
    
    error_context = ErrorContextInfo()
    
    try:
        current_time_error = get_current_time(error_context)
        print(f"Exception case get_current_time returned: {current_time_error}")
        print(f"Exception case g_current_bar_time: {g_current_bar_time}")
        
        if g_current_bar_time is not None:
            print("✅ g_current_bar_time is correctly set in exception case")
            time_str = g_current_bar_time.strftime('%Y%m%d')
            print(f"✅ Exception case g_current_bar_time.strftime('%Y%m%d') = {time_str}")
        else:
            print("❌ g_current_bar_time not set in exception case")
    except Exception as e:
        print(f"Error when testing exception case: {str(e)}")
    
    print("\n🎉 Test completed!")

if __name__ == "__main__":
    test_fix()
