# -*- coding: utf-8 -*-
"""
测试激进策略的完整逻辑
"""

import re

def check_aggressive_logic():
    """检查激进策略的完整逻辑实现"""
    print("=" * 60)
    print("检查激进策略完整逻辑实现")
    print("=" * 60)
    
    try:
        with open('aggressive_strategy.py', 'r', encoding='gbk', errors='ignore') as f:
            content = f.read()
        
        # 1. 检查execute_trading_logic函数是否包含去重检查
        print("1. 检查execute_trading_logic函数的去重机制：")
        
        trading_logic_pattern = r'def execute_trading_logic.*?(?=def|\Z)'
        trading_logic_match = re.search(trading_logic_pattern, content, re.DOTALL)
        
        if trading_logic_match:
            trading_logic_content = trading_logic_match.group(0)
            
            # 检查关键的去重检查
            checks = [
                ('当天交易检查', 'check_today_trading_records'),
                ('阶段交易检查', 'has_traded_in_current_phase'),
                ('信号状态分析', '上次信号.*当前买入信号.*当前卖出信号'),
                ('状态转换逻辑', 'previous_signal_type.*current_buy_signal')
            ]
            
            for check_name, pattern in checks:
                if re.search(pattern, trading_logic_content):
                    print(f"  ✅ {check_name} - 已实现")
                else:
                    print(f"  ❌ {check_name} - 未实现")
        else:
            print("  ❌ 未找到execute_trading_logic函数")
        
        # 2. 检查6种状态转换逻辑
        print("\n2. 检查6种状态转换逻辑：")
        
        transitions = [
            ('无信号 -> buy', 'previous_signal_type is None and current_buy_signal'),
            ('sell -> buy', "previous_signal_type == 'EXITLONG' and current_buy_signal"),
            ('buy -> buy', "previous_signal_type == 'ENTERLONG' and current_buy_signal"),
            ('无信号 -> sell', 'previous_signal_type is None and current_sell_signal'),
            ('buy -> sell', "previous_signal_type == 'ENTERLONG' and current_sell_signal"),
            ('sell -> sell', "previous_signal_type == 'EXITLONG' and current_sell_signal")
        ]
        
        for transition_name, pattern in transitions:
            if re.search(pattern, content):
                print(f"  ✅ {transition_name} - 已实现")
            else:
                print(f"  ❌ {transition_name} - 未实现")
        
        # 3. 检查新增的去重函数
        print("\n3. 检查新增的去重函数：")
        
        new_functions = [
            'has_traded_in_current_phase',
            'determine_current_phase'
        ]
        
        for func in new_functions:
            if f"def {func}" in content:
                print(f"  ✅ {func} - 已定义")
            else:
                print(f"  ❌ {func} - 未定义")
        
        # 4. 检查激进策略的执行流程
        print("\n4. 检查激进策略执行流程：")
        
        # 查找handlebar函数中的调用
        handlebar_pattern = r'def handlebar.*?(?=def|\Z)'
        handlebar_match = re.search(handlebar_pattern, content, re.DOTALL)
        
        if handlebar_match:
            handlebar_content = handlebar_match.group(0)
            
            flow_checks = [
                ('信号检测', 'detect_signals'),
                ('交易逻辑执行', 'execute_trading_logic'),
                ('任务队列处理', 'task_queue_process_pending_tasks')
            ]
            
            for check_name, pattern in flow_checks:
                if re.search(pattern, handlebar_content):
                    print(f"  ✅ {check_name} - 已集成到handlebar")
                else:
                    print(f"  ❌ {check_name} - 未集成到handlebar")
        else:
            print("  ❌ 未找到handlebar函数")
        
        print("\n" + "=" * 60)
        print("激进策略逻辑检查完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"检查失败：{str(e)}")

if __name__ == "__main__":
    check_aggressive_logic()
