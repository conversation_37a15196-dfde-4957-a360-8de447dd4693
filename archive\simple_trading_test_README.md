# 简单交易测试策略使用说明

## 概述

这是一个基于异步交易队列的简单交易测试策略，从 `value_averaging_strategy.py` 中提取了完整的异步交易队列方案，并实现了简单的买卖操作测试。

## 主要特性

### 1. 异步交易队列系统
- **任务队列管理**：支持创建、执行、监控交易任务
- **任务状态跟踪**：PENDING → EXECUTING → WAITING_CALLBACK → COMPLETED/FAILED
- **超时处理机制**：分级超时处理（警告、查询、告警）
- **完整日志记录**：任务创建、执行、完成全过程日志

### 2. 简单交易逻辑
- **目标股票**：159915.SZ (易方达创业板ETF)
- **交易规则**：每次买卖100股
- **交替策略**：偶数次交易买入，奇数次交易卖出
- **防频繁交易**：每次交易后休眠3600秒（1小时）

### 3. 数据库支持
- **SQLite数据库**：存储任务队列、执行日志、策略状态
- **完整记录**：所有交易活动都有详细记录
- **状态持久化**：策略重启后可恢复状态

## 文件结构

```
simple_trading_test.py          # 主策略文件
simple_trading_test.db          # SQLite数据库（运行后自动创建）
simple_trading_test_README.md   # 本说明文档
```

## 核心组件

### 1. 数据库表结构

#### trade_task_queue（交易任务队列表）
- 存储所有交易任务的详细信息
- 跟踪任务状态变化
- 记录订单ID和执行时间

#### trade_task_log（任务日志表）
- 记录任务执行过程中的所有日志
- 支持不同级别的日志（DEBUG、INFO、WARNING、ERROR、CRITICAL）
- 便于问题排查和性能分析

#### trade_execution_log（交易执行日志表）
- 记录实际的交易执行结果
- 包含价格、数量、费用等详细信息

#### strategy_status（策略状态表）
- 保存策略的运行状态
- 记录最后交易时间和交易计数
- 支持休眠状态管理

### 2. 核心类

#### SimpleTradeTaskQueue
- 任务队列管理器
- 负责创建任务组和记录日志

#### SimpleTradeTaskExecutor
- 任务执行器
- 处理具体的买卖订单执行
- 管理任务状态转换和超时处理

## 使用方法

### 1. 在iQuant平台中使用

1. 将 `simple_trading_test.py` 上传到iQuant平台
2. 在策略中设置以下参数：
   - 账户ID：确保 `ACCOUNT_ID` 设置正确
   - 股票代码：默认为 159915.SZ
   - 交易股数：默认为100股
   - 休眠时间：默认为3600秒

3. 启动策略，系统会自动：
   - 初始化数据库
   - 加载历史状态
   - 开始执行交易逻辑

### 2. 交易流程

1. **首次运行**：执行买入操作
2. **休眠期**：等待3600秒
3. **第二次运行**：执行卖出操作
4. **循环执行**：买入→休眠→卖出→休眠...

### 3. 监控和调试

#### 查看日志
```python
# 在iQuant控制台中可以看到详细的执行日志
# 包括任务创建、执行状态、超时处理等信息
```

#### 数据库查询
```sql
-- 查看任务队列状态
SELECT * FROM trade_task_queue ORDER BY created_time DESC;

-- 查看执行日志
SELECT * FROM trade_task_log ORDER BY log_time DESC;

-- 查看交易记录
SELECT * FROM trade_execution_log ORDER BY trade_time DESC;

-- 查看策略状态
SELECT * FROM strategy_status ORDER BY updated_time DESC;
```

## 配置参数

### 主要参数
- `TEST_FUND_CODE`：测试股票代码（默认：159915.SZ）
- `TRADE_SHARES`：每次交易股数（默认：100股）
- `SLEEP_DURATION`：交易后休眠时间（默认：3600秒）
- `ACCOUNT_ID`：交易账户ID

### 费用参数
- `COMMISSION_FEE_RATE`：佣金费率（默认：0.0003）
- `COMMISSION_FEE_MIN`：最低佣金（默认：5元）
- `SELL_TAX_RATE`：印花税率（默认：0.001）

### 超时参数
- `WARNING`：60秒后记录警告
- `QUERY`：300秒后主动查询状态
- `ALERT`：1800秒后发送告警

## 安全特性

### 1. 防频繁交易
- 强制休眠机制，防止过度交易
- 状态持久化，重启后继续休眠

### 2. 错误处理
- 完整的异常捕获和处理
- 任务失败时的重试机制
- 详细的错误日志记录

### 3. 超时保护
- 分级超时处理机制
- 自动查询订单状态
- 严重超时时的告警机制

## 扩展说明

### 1. 自定义交易逻辑
可以修改 `execute_simple_trading_logic` 函数来实现不同的交易策略：
- 基于技术指标的买卖信号
- 基于时间的定投策略
- 基于价格的网格交易

### 2. 添加更多股票
可以扩展为多股票交易：
- 修改股票池配置
- 调整任务创建逻辑
- 增加股票间的协调机制

### 3. 集成通知功能
可以添加交易通知：
- 邮件通知
- 微信通知
- 短信通知

## 注意事项

1. **实盘风险**：这是实盘交易策略，请在充分测试后使用
2. **资金管理**：确保账户有足够资金进行交易
3. **市场时间**：注意交易时间限制
4. **监控重要**：定期检查策略运行状态和交易结果

## 技术支持

如有问题，请检查：
1. 数据库连接是否正常
2. 账户设置是否正确
3. 网络连接是否稳定
4. iQuant平台API是否可用

通过查看数据库中的日志表可以获得详细的错误信息和执行状态。
