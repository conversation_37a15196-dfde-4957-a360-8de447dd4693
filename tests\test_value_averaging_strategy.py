#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
价值平均策略测试脚本
用于验证策略的核心逻辑和防重复交易机制
"""

import sys
import os
import datetime
import sqlite3
from unittest.mock import Mock, MagicMock

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 模拟iQuant平台函数
def passorder(*args, **kwargs):
    """模拟passorder函数"""
    print(f"模拟交易: passorder({args}, {kwargs})")
    return True

def down_history_data(stock_code, period, start_date, end_date, context):
    """模拟历史数据下载函数"""
    print(f"模拟下载历史数据: {stock_code}, {period}, {start_date} - {end_date}")
    # 返回模拟的历史数据
    import datetime
    dates = []
    current = datetime.datetime.strptime(start_date, "%Y%m%d")
    end = datetime.datetime.strptime(end_date, "%Y%m%d")

    mock_data = []
    price = 100.0  # 起始价格

    while current <= end:
        if current.weekday() < 5:  # 工作日
            price += (hash(current.strftime("%Y%m%d")) % 21 - 10) * 0.01  # 模拟价格波动
            mock_data.append({
                'date': current.strftime("%Y%m%d"),
                'open': price,
                'high': price * 1.02,
                'low': price * 0.98,
                'close': price,
                'volume': 1000000
            })
        current += datetime.timedelta(days=1)

    return mock_data

# 将模拟函数添加到全局命名空间
import builtins
builtins.passorder = passorder
builtins.down_history_data = down_history_data

# 导入策略模块
try:
    from value_averaging_strategy import (
        init,
        handlebar,
        is_trade_executed_today,
        mark_trade_executed,
        check_and_execute_trade,
        is_valid_check_time,
        g_db_connection,
        g_strategy_status
    )
    print("✓ 策略模块导入成功")
except ImportError as e:
    print(f"✗ 策略模块导入失败: {e}")
    sys.exit(1)
except Exception as e:
    print(f"✗ 策略模块加载异常: {e}")
    sys.exit(1)


def create_mock_context_info():
    """创建模拟的ContextInfo对象"""
    context = Mock()
    context.accid = "test_account"
    context.is_last_bar = Mock(return_value=True)
    context.run_count = 0
    
    # 模拟回测模式
    context.do_back_test = True
    
    # 模拟当前时间
    context.get_bar_timetag = Mock(return_value=datetime.datetime.now().timestamp())
    
    return context


def test_database_initialization():
    """测试数据库初始化"""
    print("\n=== 测试数据库初始化 ===")
    
    try:
        # 创建模拟ContextInfo
        context = create_mock_context_info()
        
        # 调用初始化函数
        init(context)
        
        # 检查数据库连接
        if g_db_connection is not None:
            print("✓ 数据库连接成功")
            
            # 检查表是否创建
            cursor = g_db_connection.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            expected_tables = [
                'strategy_status', 'signal_history', 'trade_orders', 
                'position_records', 'trade_logs', 'account_info', 
                'skip_periods', 'daily_trade_status'
            ]
            
            for table in expected_tables:
                if table in tables:
                    print(f"✓ 表 {table} 创建成功")
                else:
                    print(f"✗ 表 {table} 未找到")
        else:
            print("✗ 数据库连接失败")
            
    except Exception as e:
        print(f"✗ 数据库初始化测试失败: {e}")


def test_anti_duplicate_mechanism():
    """测试防重复交易机制"""
    print("\n=== 测试防重复交易机制 ===")
    
    try:
        trade_date = "2024-01-15"
        
        # 测试初始状态
        if not is_trade_executed_today('buy_signal', trade_date):
            print("✓ 初始状态：买入信号未执行")
        else:
            print("✗ 初始状态异常：买入信号已标记为执行")
        
        # 标记买入信号已执行
        mark_trade_executed('buy_signal', trade_date)
        print("✓ 标记买入信号已执行")
        
        # 再次检查
        if is_trade_executed_today('buy_signal', trade_date):
            print("✓ 防重复机制工作正常：买入信号已执行")
        else:
            print("✗ 防重复机制失败：买入信号未正确标记")
        
        # 测试其他类型信号
        if not is_trade_executed_today('sell_signal', trade_date):
            print("✓ 卖出信号独立性正常：未受买入信号影响")
        else:
            print("✗ 信号独立性异常：卖出信号受买入信号影响")
            
    except Exception as e:
        print(f"✗ 防重复交易机制测试失败: {e}")


def test_time_validation():
    """测试时间验证"""
    print("\n=== 测试时间验证 ===")
    
    try:
        # 测试当前时间是否有效
        is_valid = is_valid_check_time()
        current_time = datetime.datetime.now().time()
        
        print(f"当前时间: {current_time}")
        print(f"是否为有效交易检测时间: {is_valid}")
        
        if current_time >= datetime.time(14, 45):
            if is_valid:
                print("✓ 时间验证正确：当前时间在14:45之后")
            else:
                print("✗ 时间验证错误：当前时间在14:45之后但返回False")
        else:
            if not is_valid:
                print("✓ 时间验证正确：当前时间在14:45之前")
            else:
                print("✗ 时间验证错误：当前时间在14:45之前但返回True")
                
    except Exception as e:
        print(f"✗ 时间验证测试失败: {e}")


def test_mock_trading():
    """测试模拟交易"""
    print("\n=== 测试模拟交易 ===")
    
    try:
        context = create_mock_context_info()
        trade_date = datetime.datetime.now().strftime("%Y-%m-%d")
        
        # 定义一个简单的模拟交易函数
        def mock_trade_func(ctx, *args):
            print(f"执行模拟交易，参数: {args}")
            return True
        
        # 测试交易执行
        result = check_and_execute_trade(
            context, 
            'buy_signal', 
            trade_date, 
            mock_trade_func, 
            "test_signal"
        )
        
        if result:
            print("✓ 首次交易执行成功")
        else:
            print("✗ 首次交易执行失败")
        
        # 测试重复交易防护
        result2 = check_and_execute_trade(
            context, 
            'buy_signal', 
            trade_date, 
            mock_trade_func, 
            "test_signal"
        )
        
        if not result2:
            print("✓ 防重复交易机制工作正常：重复交易被阻止")
        else:
            print("✗ 防重复交易机制失败：重复交易未被阻止")
            
    except Exception as e:
        print(f"✗ 模拟交易测试失败: {e}")


def test_strategy_status():
    """测试策略状态"""
    print("\n=== 测试策略状态 ===")
    
    try:
        if g_strategy_status is not None:
            print("✓ 策略状态对象存在")
            print(f"当前阶段: {g_strategy_status.get('current_phase', 'unknown')}")
            print(f"当前期数: {g_strategy_status.get('current_period', 0)}")
            print(f"起始日期: {g_strategy_status.get('start_period_date', 'unknown')}")
        else:
            print("✗ 策略状态对象不存在")
            
    except Exception as e:
        print(f"✗ 策略状态测试失败: {e}")


def main():
    """主测试函数"""
    print("价值平均策略测试开始")
    print("=" * 50)
    
    # 运行各项测试
    test_database_initialization()
    test_anti_duplicate_mechanism()
    test_time_validation()
    test_mock_trading()
    test_strategy_status()
    
    print("\n" + "=" * 50)
    print("测试完成")


if __name__ == "__main__":
    main()
