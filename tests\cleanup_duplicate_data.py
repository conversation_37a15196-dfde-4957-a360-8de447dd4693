# -*- coding: utf-8 -*-

"""
清理重复数据脚本
"""

import sqlite3
import datetime

def cleanup_account_info():
    """清理 account_info 表的重复记录"""
    print("🧹 清理 account_info 表重复记录...")
    
    try:
        conn = sqlite3.connect('trading_data.db')
        cursor = conn.cursor()
        
        # 查看当前重复记录
        cursor.execute("""
            SELECT account_id, COUNT(*) as count 
            FROM account_info 
            GROUP BY account_id 
            HAVING COUNT(*) > 1
        """)
        
        duplicates = cursor.fetchall()
        print(f"发现 {len(duplicates)} 个账户有重复记录")
        
        for account_id, count in duplicates:
            print(f"  账户 {account_id}: {count} 条记录")
            
            # 保留最新的记录，删除旧的
            cursor.execute("""
                DELETE FROM account_info 
                WHERE account_id = ? AND id NOT IN (
                    SELECT id FROM account_info 
                    WHERE account_id = ? 
                    ORDER BY update_time DESC 
                    LIMIT 1
                )
            """, (account_id, account_id))
            
            deleted_count = cursor.rowcount
            print(f"    删除了 {deleted_count} 条旧记录")
        
        conn.commit()
        
        # 验证清理结果
        cursor.execute("SELECT COUNT(*) FROM account_info")
        total_count = cursor.fetchone()[0]
        
        cursor.execute("""
            SELECT COUNT(DISTINCT account_id) FROM account_info
        """)
        unique_count = cursor.fetchone()[0]
        
        print(f"✅ 清理完成：总记录 {total_count}，唯一账户 {unique_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 清理失败：{str(e)}")
        return False

def fix_account_info_empty_ids():
    """修复 account_info 表中的空 account_id"""
    print("\n🔧 修复 account_info 表中的空 account_id...")
    
    try:
        conn = sqlite3.connect('trading_data.db')
        cursor = conn.cursor()
        
        # 查找空的 account_id
        cursor.execute("""
            SELECT COUNT(*) FROM account_info 
            WHERE account_id IS NULL OR account_id = '' OR account_id = 'default'
        """)
        
        empty_count = cursor.fetchone()[0]
        print(f"发现 {empty_count} 条记录的 account_id 为空或默认值")
        
        if empty_count > 0:
            # 更新为实际的账户ID（这里使用 'MAIN_ACCOUNT' 作为示例）
            cursor.execute("""
                UPDATE account_info 
                SET account_id = 'MAIN_ACCOUNT'
                WHERE account_id IS NULL OR account_id = '' OR account_id = 'default'
            """)
            
            updated_count = cursor.rowcount
            print(f"✅ 已更新 {updated_count} 条记录的 account_id")
            
            conn.commit()
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 修复失败：{str(e)}")
        return False

def check_data_consistency():
    """检查数据一致性"""
    print("\n📊 检查数据一致性...")
    
    try:
        conn = sqlite3.connect('trading_data.db')
        cursor = conn.cursor()
        
        # 检查 trade_orders 表中的 PENDING 状态订单
        cursor.execute("""
            SELECT COUNT(*) FROM trade_orders 
            WHERE order_status = 'PENDING'
        """)
        pending_orders = cursor.fetchone()[0]
        
        # 检查 trade_task_queue 表中的等待回调任务
        cursor.execute("""
            SELECT COUNT(*) FROM trade_task_queue 
            WHERE task_status = 'WAITING_CALLBACK'
        """)
        waiting_tasks = cursor.fetchone()[0]
        
        # 检查账户信息记录
        cursor.execute("SELECT COUNT(*) FROM account_info")
        account_records = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(DISTINCT account_id) FROM account_info")
        unique_accounts = cursor.fetchone()[0]
        
        print(f"📋 数据一致性检查结果：")
        print(f"   PENDING 订单：{pending_orders}")
        print(f"   等待回调任务：{waiting_tasks}")
        print(f"   账户信息记录：{account_records}")
        print(f"   唯一账户数：{unique_accounts}")
        
        # 检查是否有不一致的情况
        issues = []
        
        if account_records != unique_accounts:
            issues.append(f"账户信息有重复记录：{account_records} 记录 vs {unique_accounts} 唯一账户")
        
        if pending_orders > 0:
            issues.append(f"存在 {pending_orders} 个 PENDING 状态的订单，可能需要手动处理")
        
        if waiting_tasks > 0:
            issues.append(f"存在 {waiting_tasks} 个等待回调的任务，可能需要检查")
        
        if issues:
            print("\n⚠️  发现的问题：")
            for issue in issues:
                print(f"   - {issue}")
        else:
            print("\n✅ 数据一致性良好")
        
        conn.close()
        return len(issues) == 0
        
    except Exception as e:
        print(f"❌ 检查失败：{str(e)}")
        return False

def show_recent_data():
    """显示最近的数据"""
    print("\n📈 显示最近的数据...")
    
    try:
        conn = sqlite3.connect('trading_data.db')
        cursor = conn.cursor()
        
        # 显示最近的订单
        print("\n最近的订单 (trade_orders):")
        cursor.execute("""
            SELECT order_date, stock_code, order_type, order_status, 
                   target_shares, actual_shares, error_message
            FROM trade_orders 
            ORDER BY created_time DESC 
            LIMIT 5
        """)
        
        orders = cursor.fetchall()
        if orders:
            print("  日期        | 股票代码 | 类型 | 状态    | 目标股数 | 实际股数 | 错误信息")
            print("  " + "-" * 80)
            for order in orders:
                date, code, type_, status, target, actual, error = order
                error_short = (error[:20] + "...") if error and len(error) > 20 else (error or "")
                print(f"  {date[:10]} | {code:8} | {type_:4} | {status:7} | {target:8} | {actual:8} | {error_short}")
        else:
            print("  无订单记录")
        
        # 显示最近的任务
        print("\n最近的任务 (trade_task_queue):")
        cursor.execute("""
            SELECT created_time, task_type, stock_code, task_status, 
                   target_shares, error_message
            FROM trade_task_queue 
            ORDER BY created_time DESC 
            LIMIT 5
        """)
        
        tasks = cursor.fetchall()
        if tasks:
            print("  时间        | 任务类型          | 股票代码 | 状态      | 目标股数 | 错误信息")
            print("  " + "-" * 85)
            for task in tasks:
                time, type_, code, status, target, error = task
                error_short = (error[:15] + "...") if error and len(error) > 15 else (error or "")
                print(f"  {time[:10]} | {type_:17} | {code:8} | {status:9} | {target:8} | {error_short}")
        else:
            print("  无任务记录")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 显示数据失败：{str(e)}")

def main():
    """主函数"""
    print("🚀 开始清理重复数据和检查一致性\n")
    
    # 1. 清理账户信息重复记录
    cleanup_result = cleanup_account_info()
    
    # 2. 修复空的 account_id
    fix_result = fix_account_info_empty_ids()
    
    # 3. 检查数据一致性
    consistency_result = check_data_consistency()
    
    # 4. 显示最近的数据
    show_recent_data()
    
    # 总结
    print(f"\n📋 清理总结：")
    print(f"   账户信息清理：{'✅ 成功' if cleanup_result else '❌ 失败'}")
    print(f"   account_id 修复：{'✅ 成功' if fix_result else '❌ 失败'}")
    print(f"   数据一致性：{'✅ 良好' if consistency_result else '⚠️  有问题'}")
    
    if cleanup_result and fix_result and consistency_result:
        print("\n🎉 数据清理完成，一切正常！")
    else:
        print("\n⚠️  部分清理失败或存在数据问题，请检查。")

if __name__ == "__main__":
    main()
