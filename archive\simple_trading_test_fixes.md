# 简单交易测试策略问题修复说明

## 问题分析

根据你提供的日志，发现了以下几个关键问题：

### 1. 订单ID获取失败
```
买入订单已提交：159915.SZ 100股，订单号：-1
```
- **问题**：`get_last_order_id` 返回 -1，表示获取订单ID失败
- **原因**：参数不正确，缺少策略名称参数

### 2. 回调函数中订单ID为空
```
收到订单回调：订单ID=，状态=49
收到订单回调：订单ID=，状态=56
```
- **问题**：订单ID为空字符串
- **原因**：使用了错误的属性名 `m_strOrderID`，应该是 `m_strOrderSysID`

### 3. 成交回调数据异常
```
收到成交回调：订单ID=，成交0股，价格0.0000，金额0.00
```
- **问题**：所有成交数据都是0或空
- **原因**：使用了错误的属性名

## 修复方案

### 1. 修复 `get_last_order_id` 函数调用

**修复前：**
```python
order_id = get_last_order_id(ACCOUNT_ID, 'stock', 'order')
```

**修复后：**
```python
order_id = get_last_order_id(ACCOUNT_ID, 'CREDIT', 'ORDER', '简单交易测试策略')
```

**修复要点：**
- 账户类型改为 `'CREDIT'`（信用账户）
- 数据类型改为 `'ORDER'`（大写）
- 添加策略名称参数 `'简单交易测试策略'`
- 检查返回值是否为 '-1'（表示未找到）

### 2. 修复订单回调函数属性

**修复前：**
```python
order_id = str(getattr(orderInfo, 'm_strOrderID', ''))
order_status = getattr(orderInfo, 'm_nOrderStatus', 0)
```

**修复后：**
```python
order_id = str(getattr(orderInfo, 'm_strOrderSysID', ''))  # 正确的属性名
order_status = getattr(orderInfo, 'm_nOrderStatus', 0)
instrument_id = str(getattr(orderInfo, 'm_strInstrumentID', ''))
volume_traded = getattr(orderInfo, 'm_nVolumeTraded', 0)
volume_total = getattr(orderInfo, 'm_nVolumeTotalOriginal', 0)
```

**修复要点：**
- 订单ID属性：`m_strOrderID` → `m_strOrderSysID`
- 添加更多有用的属性获取

### 3. 修复成交回调函数属性

**修复前：**
```python
order_id = str(getattr(dealInfo, 'm_strOrderID', ''))
deal_shares = getattr(dealInfo, 'm_nDealVol', 0)
deal_price = getattr(dealInfo, 'm_dDealPrice', 0.0)
deal_amount = getattr(dealInfo, 'm_dDealAmount', 0.0)
```

**修复后：**
```python
order_id = str(getattr(dealInfo, 'm_strOrderSysID', ''))  # 正确的属性名
deal_shares = getattr(dealInfo, 'm_nVolume', 0)  # 正确的属性名
deal_price = getattr(dealInfo, 'm_dPrice', 0.0)  # 正确的属性名
deal_amount = getattr(dealInfo, 'm_dTradeAmount', 0.0)  # 正确的属性名
```

**修复要点：**
- 订单ID：`m_strOrderID` → `m_strOrderSysID`
- 成交量：`m_nDealVol` → `m_nVolume`
- 成交价：`m_dDealPrice` → `m_dPrice`
- 成交额：`m_dDealAmount` → `m_dTradeAmount`

### 4. 添加调试功能

为了更好地排查问题，添加了调试功能：

```python
# 调试：打印所有属性
print("🔍 订单对象所有属性：")
for attr in dir(orderInfo):
    if not attr.startswith('_'):
        try:
            value = getattr(orderInfo, attr)
            if not callable(value):
                print(f"   {attr} = {value}")
        except:
            pass
```

这样可以看到实际的属性名称和值，便于进一步调试。

## API文档参考

根据iQuant API文档：

### 订单对象 (order) 主要属性：
- `m_strOrderSysID`: 合同编号，委托号
- `m_nOrderStatus`: 委托状态
- `m_strInstrumentID`: 证券代码
- `m_nVolumeTraded`: 成交数量，已成交量
- `m_nVolumeTotalOriginal`: 委托量，最初委托量

### 成交对象 (deal) 主要属性：
- `m_strOrderSysID`: 合同编号，报单编号，委托号
- `m_nVolume`: 成交量
- `m_dPrice`: 成交均价
- `m_dTradeAmount`: 成交额
- `m_strInstrumentID`: 证券代码
- `m_strTradeTime`: 成交时间

### get_last_order_id 正确用法：
```python
get_last_order_id(accountID, strAccountType, strDatatype, strategyName)
```

参数说明：
- `accountID`: 资金账号
- `strAccountType`: 账号类型 ('CREDIT', 'STOCK', 'FUTURE' 等)
- `strDatatype`: 数据类型 ('ORDER', 'DEAL')
- `strategyName`: 策略名称（对应 passorder 中的 strategyName）

## 预期效果

修复后，应该能看到类似这样的日志：

```
买入订单已提交：159915.SZ 100股，订单号：***********
收到订单回调：订单ID=***********，状态=56，股票=159915.SZ，成交量=100/100
收到成交回调：订单ID=***********，股票=159915.SZ，成交100股，价格=2.4500，金额=245.00，时间=13:15:01
✓ 任务已标记完成：订单ID=***********
```

这样就能正确跟踪订单状态和成交信息了。
