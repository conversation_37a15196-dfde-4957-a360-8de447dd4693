# 激进版择时量化投资策略修正总结

## 修正背景

根据用户反馈，原始的激进版策略在交易逻辑上存在以下问题：
1. 买卖顺序不正确，没有实现"先卖后买"的异步逻辑
2. 没有区分SLEEPING_FUND_CODE只能现金购买的限制
3. 激活期买入前没有检查并卖出SLEEPING_FUND_CODE
4. 使用了回测模式而非实盘模式

## 主要修正内容

### 1. 交易逻辑修正

#### 激活期交易逻辑：
```
原始逻辑：直接买入ACTIVE_FUND_CODE
修正逻辑：
1. 检查是否持有SLEEPING_FUND_CODE
2. 如果有，先卖出SLEEPING_FUND_CODE
3. 按固定金额买入ACTIVE_FUND_CODE（可融资）
```

#### 沉睡期交易逻辑：
```
原始逻辑：同步卖出ACTIVE_FUND_CODE，立即买入SLEEPING_FUND_CODE
修正逻辑：
1. 卖出全部ACTIVE_FUND_CODE
2. 用卖出所得现金买入SLEEPING_FUND_CODE（仅现金，不融资）
```

### 2. 融资限制修正
- **ACTIVE_FUND_CODE**：可以使用现金+融资购买
- **SLEEPING_FUND_CODE**：只能使用现金购买，不能融资

### 3. 模式设置修正
- 将`IS_BACKTEST_MODE`从`True`改为`False`
- 策略现在运行在实盘模式下

### 4. 交易实现方式
- 原计划使用复杂的异步任务队列
- 实际采用简化的同步交易模式
- 确保交易顺序正确，避免资金冲突

## 技术实现细节

### 核心函数修正

1. **execute_simple_active_trade()** - 激活期简化交易
   - 先检查并卖出SLEEPING_FUND_CODE
   - 再按固定金额买入ACTIVE_FUND_CODE
   - 支持现金+融资组合购买

2. **execute_simple_sleeping_trade()** - 沉睡期简化交易
   - 先卖出全部ACTIVE_FUND_CODE
   - 再用现金买入等值SLEEPING_FUND_CODE
   - 严格限制只能现金购买

### 数据库记录
- 保留完整的交易记录功能
- 记录每次买卖操作
- 用于判断期间内是否已交易

### 错误处理
- 完善的异常处理机制
- 详细的日志记录
- 资金不足时的提示和跳过逻辑

## 关键参数

```python
# 激进版核心参数
AGGRESSIVE_INVESTMENT_AMOUNT = 100000  # 固定投资金额（元）

# 基金代码
SLEEPING_FUND_CODE = "510720.SH"    # 沉睡期基金（只能现金购买）
ACTIVE_FUND_CODE = "159967.SZ"      # 激活期基金（可融资购买）
SIGNAL_FUND_CODE = "159915.SZ"      # 信号检测基金

# 模式设置
IS_BACKTEST_MODE = False             # 实盘模式
```

## 使用注意事项

### 1. 资金管理
- 确保账户有足够的现金和融资额度
- SLEEPING_FUND_CODE购买时不会使用融资
- 系统会自动计算现金和融资的分配比例

### 2. 交易时机
- 每个信号期间只交易一次
- 基于数据库记录判断是否已交易
- 避免重复交易

### 3. 风险控制
- 实盘模式下会真实下单
- 建议先在小金额下测试
- 确保账户登录状态正常

## 与稳健版的区别

| 特性 | 稳健版 | 激进版（修正后） |
|------|--------|------------------|
| 投资方式 | 价值平均，分期投入 | 固定金额，一次性投入 |
| 交易频率 | 每周期可能调整 | 每信号期间一次 |
| 交易模式 | 异步任务队列 | 同步简化交易 |
| 融资使用 | 复杂的融资管理 | 简化的融资逻辑 |
| 适用场景 | 长期稳健投资 | 快速信号响应 |

## 测试建议

1. **参数验证**：确认所有参数设置正确
2. **小额测试**：先用小金额测试交易逻辑
3. **日志监控**：密切关注交易日志和错误信息
4. **账户检查**：定期检查账户状态和持仓情况

## 后续优化方向

1. **异步交易**：如需要，可以实现真正的异步任务队列
2. **风险控制**：增加更多的风险控制机制
3. **性能优化**：优化数据库查询和交易执行效率
4. **监控告警**：增加交易异常的监控和告警功能

---

**修正完成时间**：2024年
**修正状态**：已完成，可用于实盘交易
**建议**：在实盘使用前进行充分测试
