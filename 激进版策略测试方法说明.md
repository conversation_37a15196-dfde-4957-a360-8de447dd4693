# 1. 下面的是正规的获取到最近一次买入信号的情况（初始测试激活期买入159967用）

```python
def get_latest_signal_from_db():
    """
    从数据库获取最近一次有效信号记录

    Returns:
        dict: 最新信号信息，包含signal_type等字段，如果没有记录返回None
    """
    try:
        if g_db_connection is None:
            return None

        cursor = g_db_connection.cursor()
        cursor.execute("""
            SELECT signal_type, signal_date, signal_price, ema_value, bottom_line, top_line
            FROM signal_history
            WHERE is_valid = 1
            ORDER BY signal_date DESC, id DESC
            LIMIT 1
        """)

        result = cursor.fetchone()
        if result:
            return {
                'signal_type': result[0],
                'signal_date': result[1],
                'signal_price': result[2],
                'ema_value': result[3],
                'bottom_line': result[4],
                'top_line': result[5]
            }
        else:
            return None

    except Exception as e:
        log_message("ERROR", "信号查询", f"获取最新信号失败：{str(e)}", None)
        return None
```



# 2. 下面的是模拟一个历史卖出信号的情况（初始测试沉睡期买入510720用）

```python
def get_latest_signal_from_db():
    """
    从数据库获取最近一次有效信号记录

    Returns:
        dict: 最新信号信息，包含signal_type等字段，如果没有记录返回None
    """
    try:
        return {
            'signal_type': 'EXITLONG',
            'signal_date': '2024-09-30',
            'signal_price': 1.652,
            'ema_value': 2.026,
            'bottom_line': 1.722,
            'top_line': None
        }

    except Exception as e:
        log_message("ERROR", "信号查询", f"获取最新信号失败：{str(e)}", None)
        return None
```



# 3. 信号模拟方法

重点：get_latest_signal_from_db代表的是数据库中记录的最近一次的买/卖信号，可模拟直接返回一个假信号即可；在下面这段前面，模拟signal_result即可。

```python
# 4. 执行交易逻辑（带防重复交易机制）
try:
    execute_trading_logic(ContextInfo, signal_result)
except Exception as e:
    log_message("ERROR", "策略运行", f"交易逻辑执行异常：{str(e)}", None, ContextInfo)
    # 交易失败不应该中断策略运行，继续执行状态更新
```

示例signal_result：

```python
signal_result = {
	'has_buy_signal': True,
	'has_sell_signal': False
}
```



# 4. 测试用例

1. 【ok】初始激活

   不用特别模拟

2. 【ok】激活 -> 激活（不重复）

   不用特别模拟

3. 【ok】激活 -> 沉睡（卖 + 买）

   见第3点，模拟has_sell_signal

   当天的交易记录要改日期：

   ​	trade_execution_log -> trade_time

   ​	trade_order -> order_date

   ​	trade_task_queue -> created_time

4. 【ok】初始沉睡

   见第2点，模拟历史卖出信号即可，记得更换新数据库以防干扰

5. 沉睡 -> 沉睡（不重复）

   见第2点，模拟历史卖出信号即可

6. 沉睡 -> 激活（卖 + 买）

   见第3点，模拟has_buy_signal
