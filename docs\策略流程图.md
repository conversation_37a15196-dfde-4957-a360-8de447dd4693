# 价值平均+择时策略流程图

## 策略整体流程图

```mermaid
graph TD
    A[系统启动] --> B[init初始化]
    B --> C[数据库初始化]
    C --> D[加载策略参数]
    D --> E[handlebar主循环]
    
    E --> F{是否最后一根K线?}
    F -->|否| E
    F -->|是| G[执行核心逻辑]
    
    G --> H[更新技术指标]
    H --> I[信号检测模块]
    I --> J[交易执行模块]
    J --> K[数据库记录]
    K --> E

    subgraph "信号检测模块"
        I --> I1[获取159915季线数据]
        I1 --> I2[计算EMA指标]
        I2 --> I3[检测买入信号ENTERLONG]
        I3 --> I4[检测卖出信号EXITLONG]
        I4 --> I5[信号过滤验证]
        I5 --> I6[返回有效信号]
    end

    subgraph "交易执行模块"
        J --> J1{当前状态?}
        J1 -->|沉睡期| J2[沉睡期逻辑]
        J1 -->|激活期| J3[激活期逻辑]
        
        J2 --> J21{收到买入信号?}
        J21 -->|是| J22[进入激活期]
        J21 -->|否| J23[持有510720]
        
        J22 --> J24[价值平均计算]
        J24 --> J25[卖出510720获取资金]
        J25 --> J26[买入159915]
        
        J3 --> J31{收到卖出信号?}
        J31 -->|是| J32[清仓159915]
        J31 -->|否| J33{月末定投日?}
        
        J32 --> J34[买入510720]
        J32 --> J35[返回沉睡期]
        
        J33 -->|是| J36[价值平均调整]
        J33 -->|否| J37[无操作]
    end
```

## 详细子流程

### 1. 价值平均计算流程
```mermaid
graph TD
    A[价值平均计算] --> B[获取5年历史最高点]
    B --> C[计算当前期数]
    C --> D[计算目标金额 = 期数 × 每期投入]
    D --> E[获取当前159915持仓价值]
    E --> F[计算差额 = 目标金额 - 持仓价值]
    F --> G{差额正负?}
    G -->|正数| H[需要买入]
    G -->|负数| I[需要卖出]
    H --> J[计算买入股数/100*100]
    I --> K[计算卖出股数]
```

### 2. 资金调用优先级流程
```mermaid
graph TD
    A[需要买入资金] --> B[尝试卖出510720]
    B --> C{资金足够?}
    C -->|是| D[执行买入]
    C -->|否| E[使用账户资金]
    E --> F{资金足够?}
    F -->|是| D
    F -->|否| G[使用融资资金]
    G --> H{融资额度足够?}
    H -->|是| D
    H -->|否| I[记录跳过周期]
```

### 3. 信号过滤验证流程
```mermaid
graph TD
    A[检测到信号] --> B[获取信号类型]
    B --> C{买入信号?}
    C -->|是| D[查询过去8期历史]
    C -->|否| E[查询过去10期历史]
    D --> F{8期内有同类信号?}
    E --> G{10期内有同类信号?}
    F -->|有| H[信号无效]
    F -->|无| I[信号有效]
    G -->|有| H
    G -->|无| I
```

## 关键技术实现点

1. **每日检测季线**：在`handlebar()`中使用`ContextInfo.get_market_data()`获取159915的季线数据
2. **月末判断**：通过日期计算判断是否为月末最后交易日
3. **融资额度获取**：使用`query_credit_account()`查询融资可用额度
4. **交易执行**：使用`passorder()`函数，`opType=27`为融资买入
5. **信号过滤**：通过数据库查询历史信号实现FILTER功能
6. **价值平均**：基于5年历史最高点计算期数和目标金额

## 策略参数配置

- 沉睡期基金仓位：100%（510720）
- 每期投入金额：可配置
- 投资周期：月线（默认）
- EMA检测周期：季线（默认）
- EMA参数：35（默认，可配置2-500）
- 底部相对比例：0.85（默认）
- 顶部相对比例：1.90（默认）
- 激活期基金代码：159915
- 沉睡期基金代码：510720