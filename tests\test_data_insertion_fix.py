#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据插入修复的脚本
验证交易完成后各个表是否正确插入数据
"""

import sqlite3
import datetime
import uuid
import json
from typing import Dict

# 数据库配置
DATABASE_PATH = "gytrading2.db"

# 费用配置
COMMISSION_FEE_RATE = 0.0003     # 佣金费率（万分之3）
COMMISSION_FEE_MIN = 5           # 最低交易佣金（元）
SELL_TAX_RATE = 0.001            # 印花税率（千分之1，仅卖出）
TRANSFER_FEE_RATE = 0.00002      # 过户费率（万分之0.2，仅上海）

def calculate_trading_fees(amount: float, shares: int, trade_type: str, stock_code: str = None) -> dict:
    """计算交易费用"""
    # 佣金（买卖都收取）
    commission = max(amount * COMMISSION_FEE_RATE, COMMISSION_FEE_MIN)

    # 印花税（仅卖出时收取）
    stamp_tax = amount * SELL_TAX_RATE if trade_type == 'SELL' else 0.0

    # 过户费（根据股票代码判断）
    transfer_fee = 0.0
    if stock_code:
        # 上海股票（以6开头或者.SH结尾）收取过户费
        if stock_code.startswith('6') or stock_code.endswith('.SH'):
            transfer_fee = max(amount * TRANSFER_FEE_RATE, 1.0)  # 最低1元
        # 深圳股票（以0、2、3开头或者.SZ结尾）免收过户费
        elif stock_code.startswith(('0', '2', '3')) or stock_code.endswith('.SZ'):
            transfer_fee = 0.0

    total_fees = commission + stamp_tax + transfer_fee

    # 计算净金额
    if trade_type == 'BUY':
        net_amount = -(amount + total_fees)  # 买入总支出
    else:
        net_amount = amount - total_fees     # 卖出净收入

    return {
        'commission': commission,
        'stamp_tax': stamp_tax,
        'transfer_fee': transfer_fee,
        'total_fees': total_fees,
        'net_amount': net_amount,
        'gross_amount': amount
    }

def simulate_trade_completion():
    """模拟交易完成，测试数据插入"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        print("🧪 开始模拟交易完成测试...")
        
        # 1. 创建测试任务组
        task_group_id = str(uuid.uuid4())
        print(f"📝 创建测试任务组：{task_group_id}")
        
        # 2. 创建测试任务
        order_uuid = str(uuid.uuid4())
        cursor.execute("""
            INSERT INTO trade_task_queue
            (task_group_id, task_type, stock_code, target_shares, target_amount,
             estimated_price, estimated_fees, task_status, order_uuid, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (task_group_id, "BUY_159915_CASH", "159915.SZ", 1000, 2500.0,
              2.5, 5.0, "COMPLETED", order_uuid, current_time))
        
        task_id = cursor.lastrowid
        print(f"📝 创建测试任务：ID={task_id}, UUID={order_uuid}")
        
        # 3. 创建对应的 trade_orders 记录
        cursor.execute("""
            INSERT INTO trade_orders
            (order_date, stock_code, order_type, order_reason, target_shares,
             actual_shares, actual_price, order_status, execution_time, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (current_time, "159915.SZ", "BUY", "TEST_VALUE_AVERAGE", 1000,
              1000, 2.5, "SUCCESS", current_time, current_time))
        
        order_id = cursor.lastrowid
        print(f"📝 创建测试订单：ID={order_id}")
        
        # 4. 模拟成交数据
        deal_shares = 1000
        deal_price = 2.5
        deal_amount = deal_shares * deal_price
        stock_code = "159915.SZ"
        trade_type = "BUY"
        
        # 5. 计算费用详情
        fee_details = calculate_trading_fees(deal_amount, deal_shares, trade_type, stock_code)
        print(f"💰 费用计算结果：{fee_details}")
        
        # 6. 插入 trade_execution_log 记录
        cursor.execute("""
            INSERT INTO trade_execution_log
            (trade_time, trade_type, stock_code, shares, price, amount, fees,
             order_id, order_uuid, status, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (current_time, trade_type, stock_code, deal_shares, deal_price, deal_amount,
              fee_details['total_fees'], str(order_id), order_uuid, 'SUCCESS', current_time))
        
        execution_log_id = cursor.lastrowid
        print(f"📝 创建交易执行记录：ID={execution_log_id}")
        
        # 7. 插入 trade_fee_details 记录
        cursor.execute("""
            INSERT INTO trade_fee_details
            (execution_log_id, order_uuid, commission, stamp_tax, transfer_fee,
             other_fees, total_fees, net_amount, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (execution_log_id, order_uuid, fee_details['commission'], fee_details['stamp_tax'],
              fee_details['transfer_fee'], 0.0, fee_details['total_fees'],
              fee_details['net_amount'], current_time))
        
        print(f"📝 创建费用明细记录")
        
        # 8. 插入 position_records 记录
        cursor.execute("""
            INSERT INTO position_records
            (record_date, stock_code, shares, avg_cost, market_value, current_price,
             period_number, target_value, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (current_time, stock_code, deal_shares, deal_price, deal_amount,
              deal_price, 1, 2500.0, current_time))
        
        print(f"📝 创建持仓记录")
        
        # 9. 插入 account_info 记录
        cursor.execute("""
            INSERT OR REPLACE INTO account_info
            (account_id, total_assets, available_cash, credit_limit, credit_available,
             update_time, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, ("test_account", 100000.0, 97500.0, 50000.0, 50000.0, current_time, current_time))
        
        print(f"📝 更新账户信息")
        
        # 10. 提交所有更改
        conn.commit()
        print("✅ 所有测试数据插入完成")
        
        # 11. 验证数据
        print("\n🔍 验证插入的数据：")
        
        tables_to_check = [
            'trade_orders', 'trade_execution_log', 'trade_fee_details', 
            'position_records', 'account_info'
        ]
        
        for table in tables_to_check:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"  {table}: {count} 条记录")
            
            if count > 0:
                cursor.execute(f"SELECT * FROM {table} ORDER BY id DESC LIMIT 1")
                latest_record = cursor.fetchone()
                print(f"    最新记录: {latest_record}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败：{str(e)}")
        return False

def check_current_data():
    """检查当前数据库中的数据"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        print("📊 当前数据库状态：")
        
        tables_to_check = [
            'trade_orders', 'trade_execution_log', 'trade_fee_details', 
            'position_records', 'account_info', 'account_snapshot'
        ]
        
        for table in tables_to_check:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"  {table}: {count} 条记录")
            except Exception as e:
                print(f"  {table}: 查询失败 - {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据失败：{str(e)}")

if __name__ == "__main__":
    print("=" * 60)
    print("数据插入修复测试")
    print("=" * 60)
    
    # 检查当前状态
    check_current_data()
    
    print("\n" + "=" * 60)
    
    # 运行测试
    if simulate_trade_completion():
        print("\n✅ 测试成功！数据插入修复验证通过")
    else:
        print("\n❌ 测试失败！需要进一步检查")
    
    print("\n" + "=" * 60)
    
    # 再次检查状态
    check_current_data()
