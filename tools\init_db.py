# -*- coding: utf-8 -*-

"""
数据库初始化脚本
"""

import sqlite3
import os

def init_database():
    """初始化数据库"""
    try:
        # 连接数据库
        conn = sqlite3.connect('trading_data.db')
        cursor = conn.cursor()
        
        print("🔧 开始初始化数据库...")
        
        # 创建账户信息表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS account_info (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_id TEXT NOT NULL UNIQUE,      -- 账户ID（唯一）
                total_assets REAL NOT NULL,           -- 总资产
                available_cash REAL NOT NULL,         -- 可用现金
                credit_limit REAL,                    -- 融资额度
                credit_available REAL,                -- 可用融资
                update_time TEXT NOT NULL,            -- 更新时间
                created_time TEXT NOT NULL           -- 创建时间
            )
        """)
        
        # 创建交易指令表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_date TEXT NOT NULL,             -- 下单日期
                stock_code TEXT NOT NULL,             -- 股票代码
                order_type TEXT NOT NULL,             -- 订单类型：'BUY' 或 'SELL'
                order_reason TEXT NOT NULL,           -- 下单原因：'SIGNAL_BUY', 'SIGNAL_SELL', 'VALUE_AVERAGE'
                target_amount REAL,                   -- 目标金额
                target_shares INTEGER,                -- 目标股数
                actual_shares INTEGER,                -- 实际成交股数
                actual_price REAL,                    -- 实际成交价格
                order_status TEXT NOT NULL,           -- 订单状态：'PENDING', 'SUCCESS', 'FAILED'
                error_message TEXT,                   -- 错误信息
                execution_time TEXT,                  -- 执行时间
                created_time TEXT NOT NULL,           -- 创建时间
                retry_count INTEGER DEFAULT 0         -- 重试次数
            )
        """)
        
        # 创建交易任务队列表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_task_queue (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_group_id TEXT NOT NULL,          -- 任务组ID(UUID)
                task_type TEXT NOT NULL,              -- 任务类型: SELL_510720, BUY_159915_CASH, BUY_159915_MARGIN
                stock_code TEXT NOT NULL,             -- 股票代码
                target_shares INTEGER NOT NULL,       -- 目标股数
                target_amount REAL,                   -- 目标金额
                estimated_price REAL,                 -- 预估价格
                estimated_fees REAL,                  -- 预估费用
                task_status TEXT NOT NULL,            -- PENDING, EXECUTING, WAITING_CALLBACK, COMPLETED, FAILED, TIMEOUT
                depends_on_task TEXT,                 -- 依赖的任务ID
                order_id TEXT,                        -- 实际下单后的订单ID
                order_uuid TEXT,                      -- 订单UUID（用于回调匹配）
                task_params TEXT,                     -- 任务参数JSON
                created_time TEXT NOT NULL,           -- 创建时间
                started_time TEXT,                    -- 开始执行时间
                completed_time TEXT,                  -- 完成时间
                error_message TEXT,                   -- 错误信息
                warning_logged INTEGER DEFAULT 0,     -- 是否已记录警告
                status_queried INTEGER DEFAULT 0,     -- 是否已查询状态
                alert_sent INTEGER DEFAULT 0          -- 是否已发送告警
            )
        """)
        
        # 创建账户快照表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS account_snapshot (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_group_id TEXT NOT NULL,          -- 任务组ID
                snapshot_point TEXT NOT NULL,         -- 快照时点: BEFORE_SELL, AFTER_SELL, BEFORE_BUY, AFTER_BUY_CASH, AFTER_BUY_MARGIN
                available_cash REAL,                  -- 可用现金
                margin_available REAL,                -- 融资可用额度
                stock_510720_shares INTEGER,          -- 510720持股
                stock_510720_value REAL,              -- 510720市值
                stock_159915_shares INTEGER,          -- 159915持股
                stock_159915_value REAL,              -- 159915市值
                snapshot_time TEXT NOT NULL           -- 快照时间
            )
        """)
        
        # 创建订单状态历史表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS order_status_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_uuid TEXT NOT NULL,             -- 订单UUID
                order_id TEXT,                        -- 订单ID
                stock_code TEXT,                      -- 股票代码
                order_status INTEGER,                 -- 订单状态码
                status_desc TEXT,                     -- 状态描述
                volume_traded INTEGER,                -- 已成交量
                volume_total INTEGER,                 -- 总委托量
                callback_time TEXT,                   -- 回调时间
                created_time TEXT NOT NULL            -- 创建时间
            )
        """)
        
        # 创建交易日志表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                kline_date TEXT NOT NULL,             -- K线日期
                log_type TEXT NOT NULL,               -- 日志类型：INFO, WARNING, ERROR, DEBUG
                operation TEXT NOT NULL,              -- 操作类型
                message TEXT NOT NULL,                -- 日志消息
                details TEXT,                         -- 详细信息
                created_time TEXT NOT NULL            -- 创建时间
            )
        """)
        
        conn.commit()
        conn.close()
        
        print("✅ 数据库初始化完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据库初始化失败：{str(e)}")
        return False

if __name__ == "__main__":
    init_database()
