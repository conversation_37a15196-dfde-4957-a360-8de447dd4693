# -*- coding: utf-8 -*-
"""
一次性修复所有log_message调用
"""

import re

def fix_all_log_calls():
    """修复所有log_message调用"""
    
    # 读取文件
    with open('value_averaging_strategy.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 找到所有没有ContextInfo的log_message调用
    # 匹配模式：log_message(...) 但不包含ContextInfo
    pattern = r'log_message\([^)]+\)(?![^)]*ContextInfo)'
    
    matches = list(re.finditer(pattern, content))
    print(f"找到 {len(matches)} 个需要修复的log_message调用")
    
    # 从后往前替换，避免位置偏移
    for i, match in enumerate(reversed(matches)):
        old_call = match.group()
        
        # 检查调用的参数数量
        # 移除log_message(和最后的)
        params_str = old_call[12:-1]  # 去掉 "log_message(" 和 ")"
        
        # 简单的参数计数（不完美但足够用）
        param_count = params_str.count(',') + 1 if params_str.strip() else 0
        
        if param_count >= 3:  # 至少有log_type, operation, message
            # 在最后一个参数后添加, None, ContextInfo
            new_call = old_call[:-1] + ', None, ContextInfo)'
        else:
            # 参数不足，跳过
            print(f"跳过参数不足的调用: {old_call}")
            continue
        
        # 替换
        start_pos = match.start()
        end_pos = match.end()
        content = content[:start_pos] + new_call + content[end_pos:]
        
        if i % 20 == 0:  # 每20个打印一次进度
            print(f"已处理 {i+1}/{len(matches)} 个调用")
    
    print(f"完成！总共修复了 {len(matches)} 个log_message调用")
    
    # 写回文件
    with open('value_averaging_strategy.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("文件已更新！")
    
    return len(matches)

def main():
    """主函数"""
    print("开始批量修复所有log_message调用...")
    print("=" * 50)
    
    try:
        count = fix_all_log_calls()
        print(f"\n✅ 成功修复了 {count} 个log_message调用")
        print("现在所有log_message调用都包含ContextInfo参数了！")
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
