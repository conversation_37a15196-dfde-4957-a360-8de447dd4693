# -*- coding: utf-8 -*-
"""
简单测试激进策略配置
"""

try:
    from aggressive_strategy import TOTAL_INVESTMENT_AMOUNT, ACTIVE_FUND_CODE, SLEEPING_FUND_CODE
    
    print("=" * 50)
    print("激进策略配置验证")
    print("=" * 50)
    print(f"总投资金额：{TOTAL_INVESTMENT_AMOUNT:,}元")
    print(f"激活期基金：{ACTIVE_FUND_CODE}")
    print(f"沉睡期基金：{SLEEPING_FUND_CODE}")
    print("=" * 50)
    print("✓ 激进策略配置加载成功！")
    
except Exception as e:
    print(f"✗ 配置加载失败：{str(e)}")
    import traceback
    traceback.print_exc()
