# -*- coding: utf-8 -*-
"""
测试重试逻辑
"""

def test_retry_count_logic():
    """测试重试计数逻辑"""
    print("=" * 50)
    print("测试重试计数逻辑")
    print("=" * 50)
    
    # 模拟重试计数变化
    MAX_RETRY_COUNT = 3
    
    test_scenarios = [
        {
            'name': '任务创建',
            'initial_count': -1,
            'description': '任务创建时retry_count=-1'
        },
        {
            'name': '首次失败',
            'initial_count': -1,
            'after_increment': 0,
            'can_retry': True,
            'description': '首次失败：-1 → 0，还有3次机会'
        },
        {
            'name': '第二次失败',
            'initial_count': 0,
            'after_increment': 1,
            'can_retry': True,
            'description': '第二次失败：0 → 1，还有2次机会'
        },
        {
            'name': '第三次失败',
            'initial_count': 1,
            'after_increment': 2,
            'can_retry': True,
            'description': '第三次失败：1 → 2，还有1次机会'
        },
        {
            'name': '第四次失败',
            'initial_count': 2,
            'after_increment': 3,
            'can_retry': False,
            'description': '第四次失败：2 → 3，彻底失败'
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n{scenario['name']}:")
        print(f"  {scenario['description']}")
        
        if 'after_increment' in scenario:
            initial = scenario['initial_count']
            after = scenario['after_increment']
            can_retry = scenario['can_retry']
            
            # 验证计数逻辑
            assert after == initial + 1, f"计数错误：{initial} + 1 应该等于 {after}"
            
            # 验证重试判断逻辑
            actual_can_retry = after < MAX_RETRY_COUNT
            assert actual_can_retry == can_retry, f"重试判断错误：retry_count={after}, 应该{'能' if can_retry else '不能'}重试"
            
            print(f"  计数变化：{initial} → {after}")
            print(f"  是否可重试：{can_retry}")
            print(f"  ✓ 验证通过")
    
    print("\n" + "=" * 50)
    print("重试计数逻辑验证通过！")
    print("=" * 50)

def test_task_type_classification():
    """测试任务类型分类逻辑"""
    print("=" * 50)
    print("测试任务类型分类")
    print("=" * 50)
    
    def is_buy_task(task_type: str) -> bool:
        return 'BUY' in task_type
    
    def is_sell_task(task_type: str) -> bool:
        return 'SELL' in task_type
    
    # 测试用例
    task_types = [
        ('SELL_510720', False, True),
        ('SELL_159915', False, True),
        ('BUY_159915_CASH', True, False),
        ('BUY_159915_MARGIN', True, False),
        ('BUY_510720', True, False),
    ]
    
    for task_type, expected_buy, expected_sell in task_types:
        actual_buy = is_buy_task(task_type)
        actual_sell = is_sell_task(task_type)
        
        print(f"{task_type}:")
        print(f"  买入任务：{actual_buy} (预期：{expected_buy})")
        print(f"  卖出任务：{actual_sell} (预期：{expected_sell})")
        
        assert actual_buy == expected_buy, f"买入判断错误"
        assert actual_sell == expected_sell, f"卖出判断错误"
        print(f"  ✓ 验证通过")
    
    print("\n" + "=" * 50)
    print("任务类型分类验证通过！")
    print("=" * 50)

def test_dependency_logic():
    """测试依赖逻辑"""
    print("=" * 50)
    print("测试依赖逻辑")
    print("=" * 50)
    
    MAX_RETRY_COUNT = 3
    
    # 测试依赖任务状态判断
    test_cases = [
        {
            'name': '依赖任务已完成',
            'dep_status': 'COMPLETED',
            'dep_retry_count': -1,
            'can_execute': True
        },
        {
            'name': '依赖任务失败但未达重试上限',
            'dep_status': 'FAILED',
            'dep_retry_count': 1,
            'can_execute': False
        },
        {
            'name': '依赖任务失败且达到重试上限',
            'dep_status': 'FAILED',
            'dep_retry_count': 3,
            'can_execute': True
        },
        {
            'name': '依赖任务执行中',
            'dep_status': 'EXECUTING',
            'dep_retry_count': -1,
            'can_execute': False
        }
    ]
    
    for case in test_cases:
        print(f"\n{case['name']}:")
        dep_status = case['dep_status']
        dep_retry_count = case['dep_retry_count']
        expected = case['can_execute']
        
        # 模拟SQL条件判断
        condition1 = dep_status == 'COMPLETED'
        condition2 = dep_status == 'FAILED' and dep_retry_count >= MAX_RETRY_COUNT
        actual_can_execute = condition1 or condition2
        
        print(f"  依赖状态：{dep_status}")
        print(f"  重试计数：{dep_retry_count}")
        print(f"  条件1（已完成）：{condition1}")
        print(f"  条件2（失败且达上限）：{condition2}")
        print(f"  可执行：{actual_can_execute} (预期：{expected})")
        
        assert actual_can_execute == expected, f"依赖逻辑判断错误"
        print(f"  ✓ 验证通过")
    
    print("\n" + "=" * 50)
    print("依赖逻辑验证通过！")
    print("=" * 50)

def test_same_type_logic():
    """测试同类型任务逻辑"""
    print("=" * 50)
    print("测试同类型任务逻辑")
    print("=" * 50)
    
    def is_buy_task(task_type: str) -> bool:
        return 'BUY' in task_type
    
    def is_sell_task(task_type: str) -> bool:
        return 'SELL' in task_type
    
    # 测试任务关系
    test_relationships = [
        ('SELL_510720', 'SELL_159915', True, '卖卖关系'),
        ('BUY_159915_CASH', 'BUY_159915_MARGIN', True, '买买关系'),
        ('BUY_159915_CASH', 'BUY_510720', True, '买买关系'),
        ('SELL_510720', 'BUY_159915_CASH', False, '卖买关系'),
        ('BUY_159915_CASH', 'SELL_159915', False, '买卖关系'),
    ]
    
    for task1, task2, expected_same, description in test_relationships:
        task1_is_buy = is_buy_task(task1)
        task1_is_sell = is_sell_task(task1)
        task2_is_buy = is_buy_task(task2)
        task2_is_sell = is_sell_task(task2)
        
        actual_same = (task1_is_buy and task2_is_buy) or (task1_is_sell and task2_is_sell)
        
        print(f"\n{description}：{task1} → {task2}")
        print(f"  任务1：买入={task1_is_buy}, 卖出={task1_is_sell}")
        print(f"  任务2：买入={task2_is_buy}, 卖出={task2_is_sell}")
        print(f"  同类型：{actual_same} (预期：{expected_same})")
        
        assert actual_same == expected_same, f"同类型判断错误"
        
        if actual_same:
            print(f"  → 可以继续执行")
        else:
            print(f"  → 需要取消后续任务")
        
        print(f"  ✓ 验证通过")
    
    print("\n" + "=" * 50)
    print("同类型任务逻辑验证通过！")
    print("=" * 50)

if __name__ == "__main__":
    print("开始测试重试和依赖逻辑...")
    
    try:
        test_retry_count_logic()
        test_task_type_classification()
        test_dependency_logic()
        test_same_type_logic()
        print("\n🎉 所有逻辑测试通过！")
    except Exception as e:
        print(f"\n❌ 测试失败：{str(e)}")
        import traceback
        traceback.print_exc()
