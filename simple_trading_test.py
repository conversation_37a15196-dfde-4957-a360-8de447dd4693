# encoding:gbk
"""
简单交易测试策略 - 基于异步交易队列
从value_averaging_strategy.py中提取异步交易队列方案，实现简单的买卖操作测试
目标股票：159915.SZ (易方达创业板ETF)
交易规则：每次买卖100股，交易后sleep 3600秒防止频繁交易
"""

import sqlite3
import datetime
import json
import math
import time
import uuid
from typing import Dict, List, Tuple, Optional
import traceback
from enum import Enum

# ==================== 策略参数配置 ====================
# 测试用基金代码
TEST_FUND_CODE = "159915.SZ"      # 测试基金：易方达创业板ETF
TRADE_SHARES = 100                # 固定交易股数：100股
# 注意：策略改为每天只交易一次，不再使用固定休眠时间

# 交易执行参数
MAX_RETRY_COUNT = 3               # 最大重试次数
RETRY_INTERVAL = 300              # 重试间隔（秒）
MIN_TRADE_SHARES = 100            # 最小交易股数（必须是100的倍数）

# 交易账户信息
ACCOUNT_ID = "************"       # 设置交易账户信息
ACCOUNT_TYPE = "CREDIT"           # 交易账户类型
COMMISSION_FEE_RATE = 0.0003      # 佣金费率（万分之3）
COMMISSION_FEE_MIN = 5            # 最低交易佣金（元）
SELL_TAX_RATE = 0.001             # 印花税率（千分之1，仅卖出）
TRANSFER_FEE_RATE = 0.00002       # 过户费率（万分之0.2，仅上海）

# 数据库配置
DATABASE_PATH = "simple_trading_test.db"   # 数据库文件路径

# 回测模式配置
IS_BACKTEST_MODE = False          # False: 实盘模式

# ==================== 平台兼容性检查 ====================
IQUANT_FUNCTIONS_AVAILABLE = False
try:
    # 检查是否在iQuant环境中
    passorder
    get_trade_detail_data
    IQUANT_FUNCTIONS_AVAILABLE = True
    print("iQuant平台函数可用")
except NameError:
    print("非iQuant环境，将使用模拟函数")
    IQUANT_FUNCTIONS_AVAILABLE = False

    # 定义模拟函数
    def passorder(*args, **kwargs):
        """模拟passorder函数"""
        print(f"[模拟] passorder调用: args={args}")
        return "MOCK_ORDER_ID"

    def get_trade_detail_data(*args, **kwargs):
        """模拟get_trade_detail_data函数"""
        print(f"[模拟] get_trade_detail_data调用: args={args}")
        return {}

    def get_last_order_id(*args, **kwargs):
        """模拟get_last_order_id函数"""
        print(f"[模拟] get_last_order_id调用: args={args}")
        return f"MOCK_ORDER_{int(time.time())}"

# ==================== 全局变量 ====================
g_db_connection = None            # 数据库连接
g_current_date = None             # 当前日期（YYYY-MM-DD格式）
g_trade_count = 0                 # 交易计数器
g_today_buy_done = False          # 今天是否已经买入
g_today_sell_done = False         # 今天是否已经卖出

# ==================== 任务状态和类型枚举 ====================
class TaskStatus(Enum):
    PENDING = "PENDING"                    # 待执行
    EXECUTING = "EXECUTING"                # 执行中
    WAITING_CALLBACK = "WAITING_CALLBACK"  # 等待回调
    COMPLETED = "COMPLETED"                # 已完成
    FAILED = "FAILED"                      # 失败
    TIMEOUT = "TIMEOUT"                    # 超时

class TaskType(Enum):
    BUY_TEST = "BUY_TEST"                 # 测试买入
    SELL_TEST = "SELL_TEST"               # 测试卖出

# 订单状态映射（基于iQuant文档）
ORDER_STATUS_MAP = {
    56: 'COMPLETED',    # 已成
    54: 'CANCELLED',    # 已撤
    57: 'FAILED',       # 废单
}

# ==================== 数据库初始化 ====================
def init_database():
    """初始化SQLite数据库"""
    global g_db_connection
    
    try:
        # 连接数据库
        g_db_connection = sqlite3.connect(DATABASE_PATH)
        cursor = g_db_connection.cursor()
        
        # 创建交易任务队列表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_task_queue (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_group_id TEXT NOT NULL,          -- 任务组ID(UUID)
                task_type TEXT NOT NULL,              -- 任务类型: BUY_TEST, SELL_TEST
                stock_code TEXT NOT NULL,             -- 股票代码
                target_shares INTEGER NOT NULL,       -- 目标股数
                target_amount REAL,                   -- 目标金额
                estimated_price REAL,                 -- 预估价格
                estimated_fees REAL,                  -- 预估费用
                task_status TEXT NOT NULL,            -- PENDING, EXECUTING, WAITING_CALLBACK, COMPLETED, FAILED, TIMEOUT
                order_id TEXT,                        -- 实际下单后的订单ID
                order_uuid TEXT,                      -- 订单UUID（用于回调匹配）
                task_params TEXT,                     -- 任务参数JSON
                created_time TEXT NOT NULL,           -- 创建时间
                started_time TEXT,                    -- 开始执行时间
                completed_time TEXT,                  -- 完成时间
                error_message TEXT,                   -- 错误信息
                warning_logged INTEGER DEFAULT 0,     -- 是否已记录警告
                status_queried INTEGER DEFAULT 0,     -- 是否已查询状态
                alert_sent INTEGER DEFAULT 0          -- 是否已发送告警
            )
        """)
        
        # 创建交易任务日志表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_task_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id INTEGER,                      -- 关联trade_task_queue.id，可为空
                task_group_id TEXT,                   -- 任务组ID，用于关联整个任务组的日志
                log_level TEXT NOT NULL,              -- 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
                log_category TEXT NOT NULL,           -- 日志分类: TASK_CREATE, TASK_EXECUTE, CALLBACK, TIMEOUT, etc.
                log_message TEXT NOT NULL,            -- 日志消息
                extra_data TEXT,                      -- 额外数据JSON
                log_time TEXT NOT NULL,               -- 日志时间
                FOREIGN KEY (task_id) REFERENCES trade_task_queue (id)
            )
        """)
        
        # 创建交易执行记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_execution_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                trade_time TEXT NOT NULL,             -- 交易时间
                trade_type TEXT NOT NULL,             -- 交易类型：BUY/SELL
                stock_code TEXT NOT NULL,             -- 股票代码
                shares INTEGER NOT NULL,              -- 股数
                price REAL,                           -- 价格
                amount REAL,                          -- 金额
                fees REAL,                            -- 总费用
                order_id TEXT,                        -- 订单ID
                order_uuid TEXT,                      -- 订单UUID（用于回调匹配）
                status TEXT NOT NULL,                 -- 状态：SUCCESS/FAILED
                error_message TEXT,                   -- 错误信息
                created_time TEXT NOT NULL           -- 创建时间
            )
        """)

        # 创建交易费用明细表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_fee_details (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                execution_log_id INTEGER,            -- 关联trade_execution_log.id
                order_uuid TEXT NOT NULL,            -- 订单UUID
                commission REAL DEFAULT 0,           -- 佣金
                stamp_tax REAL DEFAULT 0,            -- 印花税
                transfer_fee REAL DEFAULT 0,         -- 过户费
                other_fees REAL DEFAULT 0,           -- 其他费用
                total_fees REAL DEFAULT 0,           -- 总费用
                net_amount REAL DEFAULT 0,           -- 净金额
                created_time TEXT NOT NULL,          -- 创建时间
                FOREIGN KEY (execution_log_id) REFERENCES trade_execution_log (id)
            )
        """)

        # 创建订单状态历史表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS order_status_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_uuid TEXT NOT NULL,            -- 订单UUID
                order_id TEXT,                       -- 订单ID
                stock_code TEXT,                     -- 股票代码
                order_status INTEGER NOT NULL,       -- 订单状态码
                status_desc TEXT,                    -- 状态描述
                volume_traded INTEGER DEFAULT 0,    -- 已成交量
                volume_total INTEGER DEFAULT 0,     -- 总委托量
                callback_time TEXT NOT NULL,        -- 回调时间
                created_time TEXT NOT NULL          -- 记录创建时间
            )
        """)
        
        # 创建策略状态表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS strategy_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                last_trade_date TEXT,                 -- 最后交易日期（YYYY-MM-DD）
                trade_count INTEGER DEFAULT 0,       -- 交易计数
                current_mode TEXT DEFAULT 'WAITING', -- 当前模式：WAITING/TRADING/COMPLETED
                today_traded INTEGER DEFAULT 0,      -- 今天是否已交易（0/1）
                created_time TEXT NOT NULL,          -- 创建时间
                updated_time TEXT NOT NULL           -- 更新时间
            )
        """)
        
        # 创建索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_task_queue_group_id ON trade_task_queue(task_group_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_task_queue_status ON trade_task_queue(task_status)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_task_log_group_id ON trade_task_log(task_group_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_execution_log_time ON trade_execution_log(trade_time)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_fee_details_uuid ON trade_fee_details(order_uuid)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_trade_fee_details_execution_id ON trade_fee_details(execution_log_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_order_status_history_uuid ON order_status_history(order_uuid)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_order_status_history_time ON order_status_history(callback_time)")
        
        g_db_connection.commit()
        print("数据库初始化完成")
        
    except Exception as e:
        error_msg = f"数据库初始化失败：{str(e)}"
        print(error_msg)
        raise e

def log_message(log_type: str, operation: str, message: str, details: Dict = None):
    """记录日志消息到数据库"""
    try:
        if g_db_connection is None:
            print(f"[{log_type}] {operation}: {message}")
            return
        
        cursor = g_db_connection.cursor()
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        details_json = json.dumps(details, ensure_ascii=False) if details else None
        
        # 这里简化处理，直接记录到trade_task_log表
        cursor.execute("""
            INSERT INTO trade_task_log (log_level, log_category, log_message, extra_data, log_time)
            VALUES (?, ?, ?, ?, ?)
        """, (log_type, operation, message, details_json, current_time))
        
        g_db_connection.commit()
        
    except Exception as e:
        print(f"记录日志失败：{str(e)}")

# ==================== 异步交易队列核心类 ====================
class SimpleTradeTaskQueue:
    """简化的交易任务队列管理器"""
    
    def __init__(self):
        self.timeout_levels = {
            'WARNING': 60,      # 1分钟：记录警告
            'QUERY': 300,       # 5分钟：主动查询状态
            'ALERT': 1800,      # 30分钟：发送告警
        }
    
    def create_task_group(self, task_type: str, stock_code: str, shares: int, reason: str) -> str:
        """创建交易任务组"""
        task_group_id = str(uuid.uuid4())
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        try:
            self.log_task_message(
                task_group_id=task_group_id,
                level="INFO",
                category="TASK_CREATE",
                message=f"创建交易任务组：{task_type} {stock_code} {shares}股，原因：{reason}"
            )
            
            return task_group_id
            
        except Exception as e:
            error_msg = f"创建任务组失败：{str(e)}"
            print(error_msg)
            self.log_task_message(
                task_group_id=task_group_id,
                level="ERROR",
                category="TASK_CREATE",
                message=error_msg
            )
            raise
    
    def log_task_message(self, task_group_id: str = None, task_id: int = None,
                        level: str = "INFO", category: str = "GENERAL",
                        message: str = "", extra_data: Dict = None):
        """记录任务日志"""
        try:
            cursor = g_db_connection.cursor()
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            cursor.execute("""
                INSERT INTO trade_task_log
                (task_id, task_group_id, log_level, log_category, log_message, extra_data, log_time)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                task_id,
                task_group_id,
                level,
                category,
                message,
                json.dumps(extra_data) if extra_data else None,
                current_time
            ))
            
            g_db_connection.commit()
            
            # 同时输出到控制台
            print(f"[{level}] {category}: {message}")
            
        except Exception as e:
            print(f"记录任务日志失败：{str(e)}")

# 全局任务队列管理器实例
g_trade_task_queue = SimpleTradeTaskQueue()

# ==================== 交易任务执行器 ====================
class SimpleTradeTaskExecutor:
    """简化的交易任务执行器"""

    def __init__(self):
        self.task_queue = g_trade_task_queue

    def create_trade_task(self, task_group_id: str, task_type: str, stock_code: str,
                         target_shares: int, reason: str) -> tuple:
        """创建单个交易任务，返回(task_id, order_uuid)"""
        try:
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 生成订单UUID用于回调匹配
            order_uuid = str(uuid.uuid4())

            # 估算价格和费用
            estimated_price = self.get_current_price(stock_code)
            target_amount = target_shares * estimated_price
            estimated_fees = self.calculate_fees(target_amount, task_type == TaskType.SELL_TEST.value)

            cursor = g_db_connection.cursor()
            cursor.execute("""
                INSERT INTO trade_task_queue
                (task_group_id, task_type, stock_code, target_shares, target_amount,
                 estimated_price, estimated_fees, task_status, order_uuid, task_params, created_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                task_group_id,
                task_type,
                stock_code,
                target_shares,
                target_amount,
                estimated_price,
                estimated_fees,
                TaskStatus.PENDING.value,
                order_uuid,
                json.dumps({'reason': reason}),
                current_time
            ))

            task_id = cursor.lastrowid
            g_db_connection.commit()

            self.task_queue.log_task_message(
                task_group_id=task_group_id,
                task_id=task_id,
                level="INFO",
                category="TASK_CREATE",
                message=f"创建任务：{task_type} {stock_code} {target_shares}股，订单UUID：{order_uuid}",
                extra_data={
                    'task_type': task_type,
                    'stock_code': stock_code,
                    'target_shares': target_shares,
                    'estimated_price': estimated_price,
                    'estimated_fees': estimated_fees,
                    'order_uuid': order_uuid
                }
            )

            return task_id, order_uuid

        except Exception as e:
            error_msg = f"创建任务失败：{str(e)}"
            self.task_queue.log_task_message(
                task_group_id=task_group_id,
                level="ERROR",
                category="TASK_CREATE",
                message=error_msg
            )
            raise

    def get_current_price(self, stock_code: str) -> float:
        """获取当前价格（简化实现）"""
        try:
            # 这里应该调用iQuant的API获取实时价格
            # 暂时返回一个模拟价格
            if IQUANT_FUNCTIONS_AVAILABLE:
                # 实际环境中应该使用类似这样的代码：
                # market_data = ContextInfo.get_market_data_ex(...)
                # return market_data[stock_code]['close'][-1]
                pass

            # 模拟价格
            return 2.5  # 159915.SZ的大概价格

        except Exception as e:
            print(f"获取价格失败：{str(e)}")
            return 2.5  # 默认价格

    def calculate_fees(self, amount: float, is_sell: bool = False) -> float:
        """计算交易费用"""
        # 手续费
        commission = max(amount * COMMISSION_FEE_RATE, COMMISSION_FEE_MIN)

        # 印花税（仅卖出时收取）
        stamp_tax = amount * SELL_TAX_RATE if is_sell else 0

        return commission + stamp_tax

    def process_pending_tasks(self, ContextInfo):
        """处理待执行的任务"""
        try:
            print("🔍 开始处理任务队列...")

            # 1. 手动检查并更新长时间等待的任务
            self.manual_check_and_update_tasks()

            # 2. 检查超时任务
            self.check_timeout_tasks()

            # 3. 查找下一个可执行的任务
            next_task = self.get_next_executable_task()

            if next_task:
                print(f"✅ 找到待执行任务：ID={next_task['id']}, 类型={next_task['task_type']}, 股票={next_task['stock_code']}")
                self.execute_task(next_task, ContextInfo)
            else:
                print("ℹ️ 没有找到待执行的任务")

        except Exception as e:
            error_msg = f"处理任务队列失败：{str(e)}"
            print(error_msg)
            log_message("ERROR", "任务执行器", error_msg)

    def get_next_executable_task(self) -> Optional[Dict]:
        """获取下一个可执行的任务"""
        try:
            cursor = g_db_connection.cursor()

            # 先查看所有任务的状态
            cursor.execute("SELECT id, task_type, stock_code, task_status, created_time FROM trade_task_queue ORDER BY created_time DESC LIMIT 5")
            all_tasks = cursor.fetchall()
            print(f"📋 最近5个任务状态：")
            for task in all_tasks:
                print(f"   ID={task[0]}, 类型={task[1]}, 股票={task[2]}, 状态={task[3]}, 创建时间={task[4]}")

            # 查找状态为PENDING的任务
            cursor.execute("""
                SELECT * FROM trade_task_queue
                WHERE task_status = 'PENDING'
                ORDER BY created_time ASC
                LIMIT 1
            """)

            result = cursor.fetchone()
            if result:
                # 将结果转换为字典
                columns = [desc[0] for desc in cursor.description]
                task_dict = dict(zip(columns, result))
                print(f"🎯 找到PENDING任务：ID={task_dict['id']}, 类型={task_dict['task_type']}")
                return task_dict
            else:
                print("❌ 没有找到PENDING状态的任务")

            return None

        except Exception as e:
            error_msg = f"查找可执行任务失败：{str(e)}"
            print(error_msg)
            log_message("ERROR", "任务执行器", error_msg)
            return None

    def execute_task(self, task: Dict, ContextInfo):
        """执行单个任务"""
        task_id = task['id']
        task_type = task['task_type']

        try:
            # 更新任务状态为执行中
            self.update_task_status(task_id, TaskStatus.EXECUTING.value)

            self.task_queue.log_task_message(
                task_group_id=task['task_group_id'],
                task_id=task_id,
                level="INFO",
                category="TASK_EXECUTE",
                message=f"开始执行任务：{task_type} {task['stock_code']} {task['target_shares']}股"
            )

            # 根据任务类型执行相应操作
            if task_type == TaskType.BUY_TEST.value:
                self.execute_buy_task(task, ContextInfo)
            elif task_type == TaskType.SELL_TEST.value:
                self.execute_sell_task(task, ContextInfo)
            else:
                raise ValueError(f"未知任务类型：{task_type}")

        except Exception as e:
            error_msg = f"执行任务失败：{str(e)}"
            self.task_queue.log_task_message(
                task_group_id=task['task_group_id'],
                task_id=task_id,
                level="ERROR",
                category="TASK_EXECUTE",
                message=error_msg
            )
            self.update_task_status(task_id, TaskStatus.FAILED.value, error_msg)

    def update_task_status(self, task_id: int, status: str, error_message: str = None):
        """更新任务状态"""
        try:
            cursor = g_db_connection.cursor()
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            if status == TaskStatus.EXECUTING.value:
                cursor.execute("""
                    UPDATE trade_task_queue
                    SET task_status = ?, started_time = ?
                    WHERE id = ?
                """, (status, current_time, task_id))
            elif status in [TaskStatus.COMPLETED.value, TaskStatus.FAILED.value, TaskStatus.TIMEOUT.value]:
                cursor.execute("""
                    UPDATE trade_task_queue
                    SET task_status = ?, completed_time = ?, error_message = ?
                    WHERE id = ?
                """, (status, current_time, error_message, task_id))
            else:
                cursor.execute("""
                    UPDATE trade_task_queue
                    SET task_status = ?, error_message = ?
                    WHERE id = ?
                """, (status, error_message, task_id))

            g_db_connection.commit()

        except Exception as e:
            error_msg = f"更新任务状态失败：{str(e)}"
            print(error_msg)
            log_message("ERROR", "任务执行器", error_msg)

    def execute_buy_task(self, task: Dict, ContextInfo):
        """执行买入任务"""
        task_id = task['id']
        stock_code = task['stock_code']
        target_shares = task['target_shares']
        order_uuid = task['order_uuid']

        try:
            # 执行买入订单，传入order_uuid
            order_id = self.place_buy_order(stock_code, target_shares, order_uuid, ContextInfo)

            if order_id:
                # 更新任务状态为等待回调
                cursor = g_db_connection.cursor()
                cursor.execute("""
                    UPDATE trade_task_queue
                    SET task_status = ?, order_id = ?
                    WHERE id = ?
                """, (TaskStatus.WAITING_CALLBACK.value, str(order_id), task_id))
                g_db_connection.commit()

                self.task_queue.log_task_message(
                    task_group_id=task['task_group_id'],
                    task_id=task_id,
                    level="INFO",
                    category="TASK_EXECUTE",
                    message=f"买入订单已提交：{stock_code} {target_shares}股，订单号：{order_id}，UUID：{order_uuid}"
                )

                # 记录交易执行日志
                self.record_trade_execution(
                    trade_type="BUY",
                    stock_code=stock_code,
                    shares=target_shares,
                    order_id=str(order_id),
                    order_uuid=order_uuid,
                    status="PENDING"
                )
            else:
                raise Exception("下单失败，未获得订单号")

        except Exception as e:
            error_msg = f"执行买入任务失败：{str(e)}"
            self.task_queue.log_task_message(
                task_group_id=task['task_group_id'],
                task_id=task_id,
                level="ERROR",
                category="TASK_EXECUTE",
                message=error_msg
            )
            self.update_task_status(task_id, TaskStatus.FAILED.value, error_msg)

    def execute_sell_task(self, task: Dict, ContextInfo):
        """执行卖出任务"""
        task_id = task['id']
        stock_code = task['stock_code']
        target_shares = task['target_shares']
        order_uuid = task['order_uuid']

        try:
            # 执行卖出订单，传入order_uuid
            order_id = self.place_sell_order(stock_code, target_shares, order_uuid, ContextInfo)

            if order_id:
                # 更新任务状态为等待回调
                cursor = g_db_connection.cursor()
                cursor.execute("""
                    UPDATE trade_task_queue
                    SET task_status = ?, order_id = ?
                    WHERE id = ?
                """, (TaskStatus.WAITING_CALLBACK.value, str(order_id), task_id))
                g_db_connection.commit()

                self.task_queue.log_task_message(
                    task_group_id=task['task_group_id'],
                    task_id=task_id,
                    level="INFO",
                    category="TASK_EXECUTE",
                    message=f"卖出订单已提交：{stock_code} {target_shares}股，订单号：{order_id}，UUID：{order_uuid}"
                )

                # 记录交易执行日志
                self.record_trade_execution(
                    trade_type="SELL",
                    stock_code=stock_code,
                    shares=target_shares,
                    order_id=str(order_id),
                    order_uuid=order_uuid,
                    status="PENDING"
                )
            else:
                raise Exception("下单失败，未获得订单号")

        except Exception as e:
            error_msg = f"执行卖出任务失败：{str(e)}"
            self.task_queue.log_task_message(
                task_group_id=task['task_group_id'],
                task_id=task_id,
                level="ERROR",
                category="TASK_EXECUTE",
                message=error_msg
            )
            self.update_task_status(task_id, TaskStatus.FAILED.value, error_msg)

    def place_buy_order(self, stock_code: str, shares: int, order_uuid: str, ContextInfo) -> Optional[str]:
        """下买入订单"""
        try:
            # 使用iQuant的passorder函数下单，第10个参数使用UUID作为备注
            result = passorder(
                33,  # 买入
                1101,
                ACCOUNT_ID,
                stock_code,
                44,  # 市价
                -1,  # 市价
                shares,
                '简单交易测试策略',
                1,
                order_uuid,  # 使用UUID作为备注，用于回调匹配
                ContextInfo if IQUANT_FUNCTIONS_AVAILABLE else None
            )

            if IQUANT_FUNCTIONS_AVAILABLE:
                # 不再使用get_last_order_id，而是通过回调中的UUID匹配
                # 这里返回一个临时ID，真正的订单ID会在回调中获取
                return f"TEMP_BUY_{order_uuid[:8]}"
            else:
                # 模拟环境返回模拟订单号
                return f"MOCK_BUY_ORDER_{int(time.time())}"

        except Exception as e:
            print(f"下买入订单失败：{str(e)}")
            return None

    def place_sell_order(self, stock_code: str, shares: int, order_uuid: str, ContextInfo) -> Optional[str]:
        """下卖出订单"""
        try:
            # 使用iQuant的passorder函数下单，第10个参数使用UUID作为备注
            result = passorder(
                34,  # 卖出
                1101,
                ACCOUNT_ID,
                stock_code,
                44,  # 市价
                -1,  # 市价
                shares,
                '简单交易测试策略',
                1,
                order_uuid,  # 使用UUID作为备注，用于回调匹配
                ContextInfo if IQUANT_FUNCTIONS_AVAILABLE else None
            )

            if IQUANT_FUNCTIONS_AVAILABLE:
                # 不再使用get_last_order_id，而是通过回调中的UUID匹配
                # 这里返回一个临时ID，真正的订单ID会在回调中获取
                return f"TEMP_SELL_{order_uuid[:8]}"
            else:
                # 模拟环境返回模拟订单号
                return f"MOCK_SELL_ORDER_{int(time.time())}"

        except Exception as e:
            print(f"下卖出订单失败：{str(e)}")
            return None

    def record_trade_execution(self, trade_type: str, stock_code: str, shares: int,
                              order_id: str, order_uuid: str, status: str, price: float = None,
                              amount: float = None, fees: float = None, error_message: str = None):
        """记录交易执行日志"""
        try:
            cursor = g_db_connection.cursor()
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            cursor.execute("""
                INSERT INTO trade_execution_log
                (trade_time, trade_type, stock_code, shares, price, amount, fees,
                 order_id, order_uuid, status, error_message, created_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                current_time, trade_type, stock_code, shares, price, amount, fees,
                order_id, order_uuid, status, error_message, current_time
            ))

            g_db_connection.commit()

        except Exception as e:
            print(f"记录交易执行日志失败：{str(e)}")

    def check_timeout_tasks(self):
        """检查超时任务"""
        try:
            cursor = g_db_connection.cursor()
            current_time = datetime.datetime.now()

            # 查找等待回调的任务
            cursor.execute("""
                SELECT id, task_group_id, started_time, order_id, stock_code, target_shares,
                       warning_logged, status_queried, alert_sent
                FROM trade_task_queue
                WHERE task_status = 'WAITING_CALLBACK'
                AND started_time IS NOT NULL
            """)

            waiting_tasks = cursor.fetchall()

            for task_row in waiting_tasks:
                task_id, task_group_id, started_time_str, order_id, stock_code, target_shares, warning_logged, status_queried, alert_sent = task_row

                # 计算等待时间
                started_time = datetime.datetime.strptime(started_time_str, "%Y-%m-%d %H:%M:%S")
                elapsed_seconds = (current_time - started_time).total_seconds()

                # 分级处理超时
                if elapsed_seconds > self.task_queue.timeout_levels['ALERT'] and not alert_sent:
                    self.handle_critical_timeout(task_id, task_group_id, order_id, elapsed_seconds)
                elif elapsed_seconds > self.task_queue.timeout_levels['QUERY'] and not status_queried:
                    self.handle_query_timeout(task_id, task_group_id, order_id, elapsed_seconds)
                elif elapsed_seconds > self.task_queue.timeout_levels['WARNING'] and not warning_logged:
                    self.handle_warning_timeout(task_id, task_group_id, elapsed_seconds)

        except Exception as e:
            error_msg = f"检查超时任务失败：{str(e)}"
            print(error_msg)
            log_message("ERROR", "超时检查", error_msg)

    def handle_warning_timeout(self, task_id: int, task_group_id: str, elapsed_seconds: float):
        """处理警告级超时"""
        try:
            self.task_queue.log_task_message(
                task_group_id=task_group_id,
                task_id=task_id,
                level="WARNING",
                category="TIMEOUT",
                message=f"任务执行超时警告：已等待{elapsed_seconds:.0f}秒"
            )

            # 标记已记录警告
            cursor = g_db_connection.cursor()
            cursor.execute("""
                UPDATE trade_task_queue SET warning_logged = 1 WHERE id = ?
            """, (task_id,))
            g_db_connection.commit()

        except Exception as e:
            print(f"处理警告超时失败：{str(e)}")

    def handle_query_timeout(self, task_id: int, task_group_id: str, order_id: str, elapsed_seconds: float):
        """处理查询级超时"""
        try:
            self.task_queue.log_task_message(
                task_group_id=task_group_id,
                task_id=task_id,
                level="INFO",
                category="TIMEOUT",
                message=f"主动查询订单状态：已等待{elapsed_seconds:.0f}秒，订单号：{order_id}"
            )

            # 在实际环境中，这里应该查询订单状态
            # 简化处理：直接标记任务完成
            if not IQUANT_FUNCTIONS_AVAILABLE:
                self.update_task_status(task_id, TaskStatus.COMPLETED.value)
                self.task_queue.log_task_message(
                    task_group_id=task_group_id,
                    task_id=task_id,
                    level="INFO",
                    category="TIMEOUT",
                    message="模拟环境：假设订单已完成"
                )

            # 标记已查询状态
            cursor = g_db_connection.cursor()
            cursor.execute("""
                UPDATE trade_task_queue SET status_queried = 1 WHERE id = ?
            """, (task_id,))
            g_db_connection.commit()

        except Exception as e:
            print(f"处理查询超时失败：{str(e)}")

    def handle_critical_timeout(self, task_id: int, task_group_id: str, order_id: str, elapsed_seconds: float):
        """处理严重超时"""
        try:
            alert_msg = f"交易任务严重超时！任务ID：{task_id}，订单ID：{order_id}，已超时{elapsed_seconds:.0f}秒"

            self.task_queue.log_task_message(
                task_group_id=task_group_id,
                task_id=task_id,
                level="CRITICAL",
                category="TIMEOUT",
                message=alert_msg
            )

            # 发送告警
            print(f"🚨 严重告警：{alert_msg}")

            # 标记已发送告警
            cursor = g_db_connection.cursor()
            cursor.execute("""
                UPDATE trade_task_queue SET alert_sent = 1 WHERE id = ?
            """, (task_id,))
            g_db_connection.commit()

        except Exception as e:
            print(f"处理严重超时失败：{str(e)}")

    def manual_check_and_update_tasks(self):
        """手动检查并更新任务状态（用于回调失效的情况）"""
        try:
            cursor = g_db_connection.cursor()

            # 查找所有WAITING_CALLBACK状态的任务
            cursor.execute("""
                SELECT id, task_group_id, order_id, stock_code, target_shares, started_time
                FROM trade_task_queue
                WHERE task_status = 'WAITING_CALLBACK'
                AND started_time IS NOT NULL
            """)

            waiting_tasks = cursor.fetchall()

            if waiting_tasks:
                print(f"🔍 发现{len(waiting_tasks)}个等待回调的任务，尝试手动检查状态...")

                for task in waiting_tasks:
                    task_id, task_group_id, order_id, stock_code, target_shares, started_time = task

                    # 检查任务是否超时太久（超过30分钟强制完成）
                    started_time_obj = datetime.datetime.strptime(started_time, "%Y-%m-%d %H:%M:%S")
                    elapsed_seconds = (datetime.datetime.now() - started_time_obj).total_seconds()

                    if elapsed_seconds > 1800:  # 30分钟
                        print(f"⚠️ 任务{task_id}超时过久({elapsed_seconds:.0f}秒)，强制标记为完成")

                        # 强制标记为完成
                        cursor.execute("""
                            UPDATE trade_task_queue
                            SET task_status = 'COMPLETED', completed_time = ?
                            WHERE id = ?
                        """, (datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"), task_id))

                        # 同时更新执行记录
                        cursor.execute("""
                            UPDATE trade_execution_log
                            SET status = 'SUCCESS'
                            WHERE order_id = ? AND status = 'PENDING'
                        """, (order_id,))

                        g_db_connection.commit()

                        self.task_queue.log_task_message(
                            task_group_id=task_group_id,
                            task_id=task_id,
                            level="WARNING",
                            category="MANUAL_UPDATE",
                            message=f"任务超时过久，手动标记为完成：订单ID={order_id}"
                        )

                        print(f"✓ 任务{task_id}已手动标记为完成")
                    else:
                        print(f"ℹ️ 任务{task_id}等待时间{elapsed_seconds:.0f}秒，继续等待")
            else:
                print("ℹ️ 没有等待回调的任务")

        except Exception as e:
            print(f"手动检查任务状态失败：{str(e)}")
            log_message("ERROR", "手动检查", f"手动检查任务状态失败：{str(e)}")

# 全局任务执行器实例
g_trade_task_executor = SimpleTradeTaskExecutor()

# ==================== 策略状态管理 ====================
def init_strategy_status():
    """初始化策略状态（仅使用全局变量）"""
    global g_current_date, g_trade_count, g_today_buy_done, g_today_sell_done

    try:
        current_date = datetime.datetime.now().strftime("%Y-%m-%d")

        # 如果是新的一天，重置交易状态
        if g_current_date != current_date:
            g_current_date = current_date
            g_today_buy_done = False
            g_today_sell_done = False
            print(f"新的一天({current_date})开始，重置交易状态")

        # 从数据库加载历史交易计数（仅用于显示）
        try:
            cursor = g_db_connection.cursor()
            cursor.execute("SELECT COUNT(*) FROM trade_execution_log WHERE status = 'SUCCESS'")
            result = cursor.fetchone()
            g_trade_count = result[0] if result else 0
        except Exception as e:
            print(f"加载历史交易计数失败：{str(e)}")
            g_trade_count = 0

        print(f"策略状态初始化完成：")
        print(f"  当前日期：{g_current_date}")
        print(f"  今天买入状态：{'已完成' if g_today_buy_done else '未完成'}")
        print(f"  今天卖出状态：{'已完成' if g_today_sell_done else '未完成'}")
        print(f"  历史交易总数：{g_trade_count}")

    except Exception as e:
        print(f"初始化策略状态失败：{str(e)}")
        g_current_date = datetime.datetime.now().strftime("%Y-%m-%d")
        g_trade_count = 0
        g_today_buy_done = False
        g_today_sell_done = False

def check_new_day():
    """检查是否是新的一天，如果是则重置交易状态"""
    global g_current_date, g_today_buy_done, g_today_sell_done

    current_date = datetime.datetime.now().strftime("%Y-%m-%d")

    if g_current_date != current_date:
        print(f"检测到新的一天：{g_current_date} → {current_date}")
        g_current_date = current_date
        g_today_buy_done = False
        g_today_sell_done = False
        print("✓ 已重置今天的交易状态")
        return True

    return False

def can_buy_today() -> bool:
    """检查今天是否可以买入"""
    global g_today_buy_done

    # 检查是否是新的一天
    check_new_day()

    return not g_today_buy_done

def can_sell_today() -> bool:
    """检查今天是否可以卖出"""
    global g_today_sell_done

    # 检查是否是新的一天
    check_new_day()

    return not g_today_sell_done

def mark_buy_done():
    """标记今天买入已完成"""
    global g_today_buy_done

    g_today_buy_done = True
    current_date = datetime.datetime.now().strftime("%Y-%m-%d")
    print(f"✓ 已标记今天({current_date})买入完成")

def mark_sell_done():
    """标记今天卖出已完成"""
    global g_today_sell_done

    g_today_sell_done = True
    current_date = datetime.datetime.now().strftime("%Y-%m-%d")
    print(f"✓ 已标记今天({current_date})卖出完成")

def get_next_trade_type() -> str:
    """获取下一个可执行的交易类型"""
    if can_buy_today():
        return "BUY"
    elif can_sell_today():
        return "SELL"
    else:
        return None

def get_trading_fee_summary():
    """获取交易费用统计摘要"""
    try:
        cursor = g_db_connection.cursor()

        # 查询费用统计
        cursor.execute("""
            SELECT
                COUNT(*) as trade_count,
                SUM(commission) as total_commission,
                SUM(stamp_tax) as total_stamp_tax,
                SUM(transfer_fee) as total_transfer_fee,
                SUM(total_fees) as total_all_fees,
                SUM(net_amount) as total_net_amount
            FROM trade_fee_details
        """)

        result = cursor.fetchone()
        if result and result[0] > 0:
            trade_count, total_commission, total_stamp_tax, total_transfer_fee, total_all_fees, total_net_amount = result

            print(f"\n📊 交易费用统计摘要：")
            print(f"  总交易次数：{trade_count}")
            print(f"  总佣金：{total_commission:.2f}元")
            print(f"  总印花税：{total_stamp_tax:.2f}元")
            print(f"  总过户费：{total_transfer_fee:.2f}元")
            print(f"  总费用：{total_all_fees:.2f}元")
            print(f"  净交易额：{total_net_amount:.2f}元")

            if trade_count > 0:
                avg_fee = total_all_fees / trade_count
                print(f"  平均每笔费用：{avg_fee:.2f}元")
        else:
            print(f"\n📊 暂无交易费用记录")

    except Exception as e:
        print(f"获取交易费用统计失败：{str(e)}")

def calculate_trading_fees(amount: float, shares: int, trade_type: str, stock_code: str = None) -> dict:
    """计算交易费用"""
    # 佣金（买卖都收取）
    commission = max(amount * COMMISSION_FEE_RATE, COMMISSION_FEE_MIN)

    # 印花税（仅卖出时收取）
    stamp_tax = amount * SELL_TAX_RATE if trade_type == 'SELL' else 0.0

    # 过户费（根据股票代码判断）
    transfer_fee = 0.0
    if stock_code:
        # 上海股票（以6开头或者.SH结尾）收取过户费
        if stock_code.startswith('6') or stock_code.endswith('.SH'):
            transfer_fee = max(amount * TRANSFER_FEE_RATE, 1.0)  # 最低1元
        # 深圳股票（以0、2、3开头或者.SZ结尾）免收过户费
        elif stock_code.startswith(('0', '2', '3')) or stock_code.endswith('.SZ'):
            transfer_fee = 0.0

    total_fees = commission + stamp_tax + transfer_fee

    # 计算净金额（买入时为负值，表示支出；卖出时为正值，表示收入）
    if trade_type == 'BUY':
        net_amount = -(amount + total_fees)  # 买入总支出
    else:
        net_amount = amount - total_fees     # 卖出净收入

    return {
        'commission': commission,
        'stamp_tax': stamp_tax,
        'transfer_fee': transfer_fee,
        'total_fees': total_fees,
        'net_amount': net_amount,
        'gross_amount': amount
    }

def print_recent_trades(limit: int = 5):
    """打印最近的交易记录"""
    try:
        cursor = g_db_connection.cursor()

        cursor.execute("""
            SELECT
                e.trade_time, e.trade_type, e.stock_code, e.shares, e.price, e.amount,
                f.commission, f.stamp_tax, f.transfer_fee, f.total_fees, f.net_amount
            FROM trade_execution_log e
            LEFT JOIN trade_fee_details f ON e.order_uuid = f.order_uuid
            WHERE e.status = 'SUCCESS'
            ORDER BY e.trade_time DESC
            LIMIT ?
        """, (limit,))

        trades = cursor.fetchall()

        if trades:
            print(f"\n📈 最近{len(trades)}笔交易记录：")
            print("=" * 80)
            for trade in trades:
                trade_time, trade_type, stock_code, shares, price, amount, commission, stamp_tax, transfer_fee, total_fees, net_amount = trade

                type_desc = "买入" if trade_type == "BUY" else "卖出"
                print(f"{trade_time} | {type_desc} {stock_code}")
                print(f"  数量：{shares}股 × {price:.4f}元 = {amount:.2f}元")
                if commission or stamp_tax or transfer_fee:
                    fee_desc = f"佣金{commission:.2f}"
                    if stamp_tax > 0:
                        fee_desc += f" + 印花税{stamp_tax:.2f}"
                    if transfer_fee > 0:
                        fee_desc += f" + 过户费{transfer_fee:.2f}"
                    print(f"  费用：{fee_desc} = {total_fees:.2f}元")
                    print(f"  净额：{net_amount:.2f}元")
                else:
                    print(f"  费用：未记录详细信息")
                print("-" * 80)
        else:
            print(f"\n📈 暂无交易记录")

    except Exception as e:
        print(f"获取交易记录失败：{str(e)}")

# ==================== 主策略函数 ====================
def init(ContextInfo):
    """策略初始化函数"""
    try:
        print("=" * 60)
        print("简单交易测试策略 - 初始化开始")
        print("=" * 60)

        # 设置股票池
        ContextInfo.set_universe([TEST_FUND_CODE])

        # 1. 初始化数据库
        print("正在初始化数据库...")
        init_database()
        print("✓ 数据库初始化完成")

        # 2. 初始化策略状态
        print("正在初始化策略状态...")
        init_strategy_status()
        print("✓ 策略状态初始化完成")

        # 3. 设置账户信息
        ContextInfo.set_account(ACCOUNT_ID)
        print(f"已设置交易账户:{ACCOUNT_ID}")

        # 4. 显示策略配置信息
        print("\n策略配置信息：")
        print(f"  测试基金：{TEST_FUND_CODE} (易方达创业板ETF)")
        print(f"  交易股数：{TRADE_SHARES}股")
        print(f"  交易频率：每天最多交易一次")
        print(f"  交易账户：{ACCOUNT_ID}")

        # 5. 显示当前状态
        print(f"\n当前策略状态：")
        print(f"  历史交易总数：{g_trade_count}")
        print(f"  当前日期：{g_current_date}")
        print(f"  今天买入状态：{'已完成' if g_today_buy_done else '未完成'}")
        print(f"  今天卖出状态：{'已完成' if g_today_sell_done else '未完成'}")

        next_trade = get_next_trade_type()
        if next_trade:
            print(f"  下次可执行：{next_trade}（{'买入' if next_trade == 'BUY' else '卖出'}）")
        else:
            print(f"  当前状态：今天买卖都已完成，不会再交易")

        # 6. 显示交易费用统计和最近交易记录
        get_trading_fee_summary()
        print_recent_trades(3)

        # 6. 记录初始化日志
        log_message("INFO", "策略初始化", "策略初始化成功")

        print("\n" + "=" * 60)
        print("简单交易测试策略初始化完成！")
        print("=" * 60)

    except Exception as e:
        error_msg = f"策略初始化失败：{str(e)}"
        print(f"\n❌ {error_msg}")
        print("=" * 60)
        log_message("ERROR", "策略初始化", error_msg)
        raise e

def handlebar(ContextInfo):
    """主策略逻辑函数"""
    global g_last_trade_time, g_trade_count

    try:
        print(f'\n\n### HANDLE_BAR ### barpos = {getattr(ContextInfo, "barpos", "N/A")}')

        # 只在最后一根K线执行（实盘模式）
        if hasattr(ContextInfo, 'is_last_bar') and not ContextInfo.is_last_bar():
            return

        current_time = datetime.datetime.now()
        current_time_str = current_time.strftime("%Y-%m-%d %H:%M:%S")

        print(f"[实盘模式] 当前为最后一根K线，执行策略，时间：{current_time_str}")

        # 1. 处理任务队列中的待执行任务
        try:
            g_trade_task_executor.process_pending_tasks(ContextInfo)
        except Exception as e:
            log_message("ERROR", "策略运行", f"任务队列处理异常：{str(e)}")

        # 2. 检查今天是否可以交易
        next_trade_type = get_next_trade_type()
        if not next_trade_type:
            current_date = datetime.datetime.now().strftime("%Y-%m-%d")
            print(f"今天({current_date})买卖都已完成，跳过交易")
            log_message("INFO", "策略运行", f"今天({current_date})买卖都已完成，跳过交易")
            return

        # 3. 执行简单的买卖逻辑
        try:
            execute_simple_trading_logic(ContextInfo, next_trade_type)
        except Exception as e:
            log_message("ERROR", "策略运行", f"交易逻辑执行异常：{str(e)}")

        # 5. 记录运行日志
        log_message("INFO", "策略运行", f"策略执行完成，交易次数：{g_trade_count}")

    except Exception as e:
        error_msg = f"策略执行失败：{str(e)}"
        print(f"❌ {error_msg}")
        log_message("ERROR", "策略执行", error_msg)

def execute_simple_trading_logic(ContextInfo, trade_type: str):
    """执行简单的交易逻辑"""
    global g_trade_count

    try:
        current_time = datetime.datetime.now()
        current_date = current_time.strftime("%Y-%m-%d")

        # 根据传入的交易类型执行相应操作
        if trade_type == "BUY":
            task_type = TaskType.BUY_TEST.value
            reason = f"今日买入交易 - 测试买入"
            trade_desc = "买入"
        elif trade_type == "SELL":
            task_type = TaskType.SELL_TEST.value
            reason = f"今日卖出交易 - 测试卖出"
            trade_desc = "卖出"
        else:
            raise ValueError(f"未知交易类型：{trade_type}")

        print(f"准备执行{trade_desc}操作：{TEST_FUND_CODE} {TRADE_SHARES}股")
        print(f"交易原因：{reason}")

        # 创建交易任务组
        task_group_id = g_trade_task_queue.create_task_group(
            task_type=task_type,
            stock_code=TEST_FUND_CODE,
            shares=TRADE_SHARES,
            reason=reason
        )

        # 创建具体的交易任务
        task_id, order_uuid = g_trade_task_executor.create_trade_task(
            task_group_id=task_group_id,
            task_type=task_type,
            stock_code=TEST_FUND_CODE,
            target_shares=TRADE_SHARES,
            reason=reason
        )

        # 更新交易计数并标记相应交易完成
        g_trade_count += 1
        if trade_type == "BUY":
            mark_buy_done()
        elif trade_type == "SELL":
            mark_sell_done()

        print(f"✓ 交易任务已创建：任务组ID={task_group_id}，任务ID={task_id}，订单UUID={order_uuid}")
        print(f"✓ 已标记今天({current_date}){trade_desc}完成")

        # 立即尝试处理刚创建的任务
        print("🚀 立即尝试处理刚创建的任务...")
        try:
            g_trade_task_executor.process_pending_tasks(ContextInfo)
        except Exception as e:
            print(f"❌ 立即处理任务失败：{str(e)}")
            log_message("ERROR", "任务处理", f"立即处理任务失败：{str(e)}")

        log_message("INFO", "交易执行",
                   f"创建{trade_desc}任务：{TEST_FUND_CODE} {TRADE_SHARES}股，"
                   f"任务组ID：{task_group_id}，今日{trade_desc}已完成",
                   {
                       'trade_type': trade_type,
                       'stock_code': TEST_FUND_CODE,
                       'shares': TRADE_SHARES,
                       'task_group_id': task_group_id,
                       'task_id': task_id,
                       'order_uuid': order_uuid,
                       'trade_count': g_trade_count,
                       'trade_date': current_date
                   })

    except Exception as e:
        error_msg = f"执行交易逻辑失败：{str(e)}"
        print(f"❌ {error_msg}")
        log_message("ERROR", "交易执行", error_msg)
        raise

# ==================== 回调函数（如果需要的话） ====================
def order_callback(ContextInfo, orderInfo):
    """订单回调函数"""
    try:
        # 调试：打印所有属性
        print("🔍 订单对象所有属性：")
        for attr in dir(orderInfo):
            if not attr.startswith('_'):
                try:
                    value = getattr(orderInfo, attr)
                    if not callable(value):
                        print(f"   {attr} = {value}")
                except:
                    pass

        # 获取订单UUID（从备注字段）
        order_uuid = str(getattr(orderInfo, 'm_strRemark', ''))
        if not order_uuid:
            print("❌ 无法获取订单UUID（m_strRemark为空）")
            return

        # 尝试多种可能的订单ID属性名
        order_id = None
        possible_id_attrs = ['m_strOrderSysID', 'm_strOrderID', 'm_strOrderRef', 'm_nOrderRef']
        for attr in possible_id_attrs:
            try:
                temp_id = str(getattr(orderInfo, attr, ''))
                if temp_id and temp_id != '' and temp_id != '0':
                    order_id = temp_id
                    print(f"✓ 找到订单ID属性：{attr} = {order_id}")
                    break
            except:
                continue

        if not order_id:
            print("❌ 无法获取有效的订单ID")
            return

        order_status = getattr(orderInfo, 'm_nOrderStatus', 0)
        instrument_id = str(getattr(orderInfo, 'm_strInstrumentID', ''))
        volume_traded = getattr(orderInfo, 'm_nVolumeTraded', 0)
        volume_total = getattr(orderInfo, 'm_nVolumeTotalOriginal', 0)

        # 订单状态映射
        status_map = {
            0: "等待结束",
            48: "未报",
            49: "待报",
            50: "已报",
            51: "已报待撤",
            52: "部成待撤",
            53: "部撤",
            54: "已撤",
            55: "部成",
            56: "已成",
            57: "废单",
            86: "已确认",
            255: "未知"
        }

        status_desc = status_map.get(order_status, f"未知状态({order_status})")

        print(f"收到订单回调：UUID={order_uuid}，订单ID={order_id}，状态={order_status}({status_desc})，股票={instrument_id}，成交量={volume_traded}/{volume_total}")
        log_message("INFO", "订单回调", f"UUID={order_uuid}，订单ID={order_id}，状态={order_status}({status_desc})，股票={instrument_id}，成交量={volume_traded}/{volume_total}")

        # 记录订单状态历史
        try:
            cursor = g_db_connection.cursor()
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 插入状态历史记录
            cursor.execute("""
                INSERT INTO order_status_history
                (order_uuid, order_id, stock_code, order_status, status_desc,
                 volume_traded, volume_total, callback_time, created_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (order_uuid, order_id, instrument_id, order_status, status_desc,
                  volume_traded, volume_total, current_time, current_time))

            # 更新任务中的真实订单ID
            cursor.execute("""
                UPDATE trade_task_queue
                SET order_id = ?
                WHERE order_uuid = ? AND task_status = 'WAITING_CALLBACK'
            """, (order_id, order_uuid))

            g_db_connection.commit()

            print(f"✓ 订单状态历史已记录：UUID={order_uuid}，状态={status_desc}")
            if cursor.rowcount > 0:
                print(f"✓ 已更新任务的真实订单ID：UUID={order_uuid}，订单ID={order_id}")

        except Exception as e:
            print(f"记录订单状态历史失败：{str(e)}")

        # 处理订单最终状态
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 判断是否为最终状态
        final_statuses = {
            54: ("CANCELLED", "已撤销"),    # 已撤
            56: ("COMPLETED", "已成交"),    # 已成
            57: ("FAILED", "废单")          # 废单
        }

        if order_status in final_statuses:
            task_status, status_desc = final_statuses[order_status]

            try:
                cursor = g_db_connection.cursor()

                # 通过UUID查找对应的任务
                cursor.execute("""
                    SELECT id, task_type, stock_code FROM trade_task_queue
                    WHERE order_uuid = ? AND task_status = 'WAITING_CALLBACK'
                """, (order_uuid,))
                task = cursor.fetchone()

                if task:
                    # 更新任务状态
                    cursor.execute("""
                        UPDATE trade_task_queue
                        SET task_status = ?, completed_time = ?
                        WHERE order_uuid = ? AND task_status = 'WAITING_CALLBACK'
                    """, (task_status, current_time, order_uuid))

                    # 同时更新交易执行记录状态
                    if order_status == 56:  # 已成交
                        execution_status = "SUCCESS"
                    elif order_status == 54:  # 已撤销
                        execution_status = "CANCELLED"
                    else:  # 废单
                        execution_status = "FAILED"

                    cursor.execute("""
                        UPDATE trade_execution_log
                        SET status = ?
                        WHERE order_uuid = ? AND status = 'PENDING'
                    """, (execution_status, order_uuid))

                    g_db_connection.commit()

                    print(f"✓ 任务状态已更新：UUID={order_uuid}，任务ID={task[0]}，状态={task_status}({status_desc})")
                    print(f"✓ 执行记录状态已更新：{execution_status}")
                    log_message("INFO", "任务状态更新", f"UUID={order_uuid}的任务状态更新为{task_status}({status_desc})")
                else:
                    print(f"⚠️ 未找到匹配的任务：UUID={order_uuid}")
                    # 查询所有WAITING_CALLBACK状态的任务
                    cursor.execute("SELECT id, order_uuid, task_type FROM trade_task_queue WHERE task_status = 'WAITING_CALLBACK'")
                    waiting_tasks = cursor.fetchall()
                    print(f"当前等待回调的任务：{waiting_tasks}")

            except Exception as e:
                print(f"更新任务状态失败：{str(e)}")

        # 处理部分成交状态
        elif order_status == 55:  # 部成
            print(f"📊 订单部分成交：UUID={order_uuid}，已成交{volume_traded}/{volume_total}股")
            log_message("INFO", "部分成交", f"UUID={order_uuid}，已成交{volume_traded}/{volume_total}股")

        # 处理其他中间状态
        elif order_status in [48, 49, 50, 51, 52, 53]:
            print(f"📋 订单状态更新：UUID={order_uuid}，状态={status_desc}")
            log_message("INFO", "状态更新", f"UUID={order_uuid}，状态={status_desc}")

    except Exception as e:
        print(f"处理订单回调失败：{str(e)}")
        log_message("ERROR", "订单回调", f"处理失败：{str(e)}")

def deal_callback(ContextInfo, dealInfo):
    """成交回调函数"""
    try:
        # 调试：打印所有属性
        print("🔍 成交对象所有属性：")
        for attr in dir(dealInfo):
            if not attr.startswith('_'):
                try:
                    value = getattr(dealInfo, attr)
                    if not callable(value):
                        print(f"   {attr} = {value}")
                except:
                    pass

        # 获取订单UUID（从备注字段）
        order_uuid = str(getattr(dealInfo, 'm_strRemark', ''))
        if not order_uuid:
            print("❌ 无法获取订单UUID（m_strRemark为空）")
            return

        # 尝试多种可能的订单ID属性名
        order_id = None
        possible_id_attrs = ['m_strOrderSysID', 'm_strOrderID', 'm_strOrderRef', 'm_nOrderRef']
        for attr in possible_id_attrs:
            try:
                temp_id = str(getattr(dealInfo, attr, ''))
                if temp_id and temp_id != '' and temp_id != '0':
                    order_id = temp_id
                    print(f"✓ 找到订单ID属性：{attr} = {order_id}")
                    break
            except:
                continue

        if not order_id:
            print("❌ 无法获取有效的订单ID")
            return

        # 尝试多种可能的成交数据属性名
        deal_shares = 0
        deal_price = 0.0
        deal_amount = 0.0
        deal_commission = 0.0

        # 成交量
        for attr in ['m_nVolume', 'm_nDealVol', 'm_nTradeVolume']:
            try:
                temp_vol = getattr(dealInfo, attr, 0)
                if temp_vol > 0:
                    deal_shares = temp_vol
                    print(f"✓ 找到成交量属性：{attr} = {deal_shares}")
                    break
            except:
                continue

        # 成交价（均价）
        for attr in ['m_dPrice', 'm_dDealPrice', 'm_dTradePrice', 'm_dAvgPrice']:
            try:
                temp_price = getattr(dealInfo, attr, 0.0)
                if temp_price > 0:
                    deal_price = temp_price
                    print(f"✓ 找到成交价属性：{attr} = {deal_price}")
                    break
            except:
                continue

        # 成交额
        for attr in ['m_dTradeAmount', 'm_dDealAmount', 'm_dAmount']:
            try:
                temp_amount = getattr(dealInfo, attr, 0.0)
                if temp_amount > 0:
                    deal_amount = temp_amount
                    print(f"✓ 找到成交额属性：{attr} = {deal_amount}")
                    break
            except:
                continue

        # 手续费
        for attr in ['m_dCommission', 'm_dComssion', 'm_dFee', 'm_dTradeFee']:
            try:
                temp_commission = getattr(dealInfo, attr, 0.0)
                if temp_commission > 0:
                    deal_commission = temp_commission
                    print(f"✓ 找到手续费属性：{attr} = {deal_commission}")
                    break
            except:
                continue

        # 尝试获取其他费用信息
        transfer_fee = 0.0  # 过户费

        # 过户费
        for attr in ['m_dTransferFee', 'm_dTransFee', 'm_dSettleFee']:
            try:
                temp_transfer = getattr(dealInfo, attr, 0.0)
                if temp_transfer > 0:
                    transfer_fee = temp_transfer
                    print(f"✓ 找到过户费属性：{attr} = {transfer_fee}")
                    break
            except:
                continue

        # 计算印花税（根据交易类型和税率计算）
        stamp_tax = 0.0
        trade_type = None

        try:
            # 通过UUID查询交易类型
            cursor = g_db_connection.cursor()
            cursor.execute("""
                SELECT trade_type FROM trade_execution_log
                WHERE order_uuid = ? AND status = 'PENDING'
            """, (order_uuid,))
            result = cursor.fetchone()

            if result:
                trade_type = result[0]
                # 使用统一的费用计算函数，传入股票代码
                calculated_fees = calculate_trading_fees(deal_amount, deal_shares, trade_type, instrument_id)

                # 使用计算出的印花税和过户费
                stamp_tax = calculated_fees['stamp_tax']
                calculated_transfer_fee = calculated_fees['transfer_fee']

                if trade_type == 'SELL':
                    print(f"✓ 计算印花税：{deal_amount:.2f} × {SELL_TAX_RATE*100}% = {stamp_tax:.2f}元（卖出）")
                else:
                    print(f"✓ 印花税：{stamp_tax:.2f}元（买入免税）")

                # 如果回调中没有过户费信息，使用计算值
                if transfer_fee == 0.0 and calculated_transfer_fee > 0:
                    transfer_fee = calculated_transfer_fee
                    print(f"✓ 计算过户费：{deal_amount:.2f} × {TRANSFER_FEE_RATE*10000}‱ = {transfer_fee:.2f}元（{instrument_id}）")

                # 验证佣金计算（可选）
                calculated_commission = calculated_fees['commission']
                if abs(deal_commission - calculated_commission) > 0.01:
                    print(f"⚠️ 佣金差异：回调{deal_commission:.2f} vs 计算{calculated_commission:.2f}")

            else:
                print(f"⚠️ 无法确定交易类型，印花税按0计算")

        except Exception as e:
            print(f"计算印花税失败：{str(e)}")
            stamp_tax = 0.0

        # 计算总费用
        total_fees = deal_commission + stamp_tax + transfer_fee

        instrument_id = str(getattr(dealInfo, 'm_strInstrumentID', ''))
        trade_time = str(getattr(dealInfo, 'm_strTradeTime', ''))

        # 计算净收益（买入为负，卖出为正）
        net_amount = deal_amount - total_fees

        print(f"收到成交回调：UUID={order_uuid}，订单ID={order_id}，股票={instrument_id}")
        print(f"  成交详情：{deal_shares}股 × {deal_price:.4f}元 = {deal_amount:.2f}元")
        print(f"  费用明细：佣金{deal_commission:.2f}元")
        if stamp_tax > 0:
            print(f"           印花税{stamp_tax:.2f}元（卖出税率{SELL_TAX_RATE*100}%）")
        else:
            print(f"           印花税{stamp_tax:.2f}元（买入免税）")
        if transfer_fee > 0:
            print(f"           过户费{transfer_fee:.2f}元")
        print(f"  总费用：{total_fees:.2f}元，净额：{net_amount:.2f}元，时间：{trade_time}")

        log_message("INFO", "成交回调",
                   f"UUID={order_uuid}，订单ID={order_id}，股票={instrument_id}，"
                   f"成交{deal_shares}股，均价{deal_price:.4f}，金额{deal_amount:.2f}，"
                   f"佣金{deal_commission:.2f}，印花税{stamp_tax:.2f}，过户费{transfer_fee:.2f}，"
                   f"总费用{total_fees:.2f}，净额{net_amount:.2f}，时间={trade_time}")

        # 更新交易执行记录
        try:
            cursor = g_db_connection.cursor()

            # 先通过UUID查询是否存在匹配的记录
            cursor.execute("""
                SELECT id, trade_type, stock_code FROM trade_execution_log
                WHERE order_uuid = ? AND status = 'PENDING'
            """, (order_uuid,))
            execution_record = cursor.fetchone()

            if execution_record:
                execution_log_id = execution_record[0]

                # 更新执行记录，包含总费用
                cursor.execute("""
                    UPDATE trade_execution_log
                    SET order_id = ?, price = ?, amount = ?, fees = ?, status = 'SUCCESS'
                    WHERE order_uuid = ? AND status = 'PENDING'
                """, (order_id, deal_price, deal_amount, total_fees, order_uuid))

                # 插入费用明细记录
                current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                cursor.execute("""
                    INSERT INTO trade_fee_details
                    (execution_log_id, order_uuid, commission, stamp_tax, transfer_fee,
                     other_fees, total_fees, net_amount, created_time)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (execution_log_id, order_uuid, deal_commission, stamp_tax, transfer_fee,
                      0.0, total_fees, net_amount, current_time))

                g_db_connection.commit()

                print(f"✓ 交易执行记录已更新：UUID={order_uuid}，订单ID={order_id}，记录ID={execution_log_id}")
                print(f"  记录详情：均价{deal_price:.4f}，金额{deal_amount:.2f}，总费用{total_fees:.2f}，净额{net_amount:.2f}")
                print(f"✓ 费用明细已记录：佣金{deal_commission:.2f}，印花税{stamp_tax:.2f}，过户费{transfer_fee:.2f}")
            else:
                print(f"⚠️ 未找到匹配的执行记录：UUID={order_uuid}")
                # 查询所有PENDING状态的记录
                cursor.execute("SELECT id, order_uuid, trade_type FROM trade_execution_log WHERE status = 'PENDING'")
                pending_records = cursor.fetchall()
                print(f"当前待更新的执行记录：{pending_records}")

        except Exception as e:
            print(f"更新交易执行记录失败：{str(e)}")

        # 同时尝试更新任务状态（如果订单回调没有处理）
        try:
            cursor = g_db_connection.cursor()
            cursor.execute("""
                UPDATE trade_task_queue
                SET task_status = 'COMPLETED', completed_time = ?
                WHERE order_uuid = ? AND task_status = 'WAITING_CALLBACK'
            """, (datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"), order_uuid))
            g_db_connection.commit()

            if cursor.rowcount > 0:
                print(f"✓ 通过成交回调更新任务状态：UUID={order_uuid}")

        except Exception as e:
            print(f"通过成交回调更新任务状态失败：{str(e)}")

    except Exception as e:
        print(f"处理成交回调失败：{str(e)}")
        log_message("ERROR", "成交回调", f"处理失败：{str(e)}")

print("简单交易测试策略模块加载完成！")
