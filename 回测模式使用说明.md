# 回测模式使用说明

## 🎯 简单配置方式

### 📝 **一行配置搞定**

在 `value_averaging_strategy.py` 文件顶部找到：

```python
# 回测模式配置
IS_BACKTEST_MODE = False        # True: 回测模式（不真实下单）, False: 实盘模式
```

### 🔄 **使用方法**

#### **回测时**
```python
IS_BACKTEST_MODE = True   # 设置为 True
```

#### **实盘时**  
```python
IS_BACKTEST_MODE = False  # 设置为 False
```

## ✅ **回测安全保障**

### **绝对不会真实下单**
- ✅ 回测模式下所有交易都是**模拟执行**
- ✅ 日志清晰标识：`[回测模拟]` vs `[实盘交易]`
- ✅ 数据库记录状态为 `SIMULATED`

### **示例日志输出**
```
[回测模拟] BUY 159915 1000股 价格=2.1234 金额=2123.40 原因=VALUE_AVERAGE
[回测模拟] 买入后持仓：159915 3000股 平均成本=2.1100 市值=6369.00
```

## 🚀 **使用流程**

### **步骤1：设置回测模式**
```python
IS_BACKTEST_MODE = True  # 开启回测模式
```

### **步骤2：运行策略**
- 策略会自动检测回测模式
- 所有交易都是模拟执行
- 不会有任何真实下单

### **步骤3：查看结果**
- 查看日志中的 `[回测模拟]` 标识
- 检查数据库中的 `SIMULATED` 状态记录

### **步骤4：切换实盘**
```python
IS_BACKTEST_MODE = False  # 切换到实盘模式
```

## 🎯 **核心特性**

### **时间管理**
- **回测模式**：使用K线时间作为"当前时间"
- **实盘模式**：使用系统当前时间

### **交易执行**
- **回测模式**：只做模拟记录，绝不真实下单
- **实盘模式**：执行真实交易

### **数据安全**
- **回测模式**：严格使用历史数据，避免未来数据泄露
- **实盘模式**：使用实时数据

## 📊 **完整适配状态**

### ✅ **已完成适配**
1. ✅ 交易执行安全（最重要）
2. ✅ 时间管理统一
3. ✅ 日志标识清晰
4. ✅ 数据库记录区分
5. ✅ 持仓模拟计算

### **使用建议**
- 回测时务必设置 `IS_BACKTEST_MODE = True`
- 实盘前务必设置 `IS_BACKTEST_MODE = False`
- 观察日志确认模式正确

**简单、安全、可靠！**
