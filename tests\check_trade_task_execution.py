#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def check_trade_task_execution_table():
    """检查 trade_task_execution 和 trade_task_log 表的使用情况和区别"""
    
    # 查找数据库文件
    db_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.db'):
                db_files.append(os.path.join(root, file))

    print('找到的数据库文件：')
    for db_file in db_files:
        print(f'  {db_file}')

    # 检查所有数据库文件
    for db_file in db_files:
        print(f'\n检查数据库：{db_file}')
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()

            # 检查两个表的存在情况
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name IN ('trade_task_execution', 'trade_task_log')")
            tables = cursor.fetchall()
            table_names = [table[0] for table in tables]

            print(f'存在的表：{table_names}')

            # 检查 trade_task_execution 表
            if 'trade_task_execution' in table_names:
                print('\n=== trade_task_execution 表分析 ===')
                cursor.execute('PRAGMA table_info(trade_task_execution)')
                columns = cursor.fetchall()
                print('字段结构：')
                for col in columns:
                    print(f'  {col[1]} {col[2]}')

                cursor.execute('SELECT COUNT(*) FROM trade_task_execution')
                count = cursor.fetchone()[0]
                print(f'数据量：{count} 条记录')

                if count > 0:
                    cursor.execute('SELECT DISTINCT execution_step FROM trade_task_execution')
                    steps = cursor.fetchall()
                    print('执行步骤类型：')
                    for step in steps:
                        print(f'  {step[0]}')
            else:
                print('\n✗ trade_task_execution 表不存在')

            # 检查 trade_task_log 表
            if 'trade_task_log' in table_names:
                print('\n=== trade_task_log 表分析 ===')
                cursor.execute('PRAGMA table_info(trade_task_log)')
                columns = cursor.fetchall()
                print('字段结构：')
                for col in columns:
                    print(f'  {col[1]} {col[2]}')

                cursor.execute('SELECT COUNT(*) FROM trade_task_log')
                count = cursor.fetchone()[0]
                print(f'数据量：{count} 条记录')

                if count > 0:
                    cursor.execute('SELECT DISTINCT log_category FROM trade_task_log')
                    categories = cursor.fetchall()
                    print('日志分类：')
                    for cat in categories:
                        print(f'  {cat[0]}')

                    cursor.execute('SELECT log_category, COUNT(*) FROM trade_task_log GROUP BY log_category')
                    cat_counts = cursor.fetchall()
                    print('各分类数量：')
                    for cat, count in cat_counts:
                        print(f'  {cat}: {count} 条')
            else:
                print('\n✗ trade_task_log 表不存在')

            # 检查相关的 trade_task_queue 表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='trade_task_queue'")
            queue_exists = cursor.fetchone()
            if queue_exists:
                print('\n=== trade_task_queue 表状态 ===')
                cursor.execute('SELECT COUNT(*) FROM trade_task_queue')
                queue_count = cursor.fetchone()[0]
                print(f'数据量：{queue_count} 条记录')

                if queue_count > 0:
                    cursor.execute('SELECT task_status, COUNT(*) FROM trade_task_queue GROUP BY task_status')
                    status_counts = cursor.fetchall()
                    print('任务状态分布：')
                    for status, count in status_counts:
                        print(f'  {status}: {count} 条')

            conn.close()
        except Exception as e:
            print(f'检查数据库 {db_file} 时出错：{e}')

if __name__ == "__main__":
    check_trade_task_execution_table()
