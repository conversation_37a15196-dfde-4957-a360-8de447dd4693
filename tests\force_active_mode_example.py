# -*- coding: utf-8 -*-
"""
强制激活模式使用示例
演示如何让策略直接进入激活期，适用于客户部署场景
"""

import sqlite3
import datetime
from value_averaging_strategy import (
    init_database, load_strategy_status, g_strategy_status, g_db_connection,
    force_activate_strategy, manual_set_active_status, insert_simulated_buy_signal,
    log_message, update_strategy_status
)

def example_1_force_active_mode():
    """
    示例1：使用配置参数强制激活
    这是最简单的方法，适合客户部署
    """
    print("=" * 60)
    print("示例1：配置参数强制激活模式")
    print("=" * 60)
    
    print("使用方法：")
    print("1. 修改 value_averaging_strategy.py 中的配置参数：")
    print("   FORCE_ACTIVE_MODE = True")
    print("   FORCE_ACTIVE_START_DATE = '2024-06-28'")
    print("   FORCE_ACTIVE_START_PRICE = 1.234")
    print("")
    print("2. 运行策略，系统会自动：")
    print("   - 在数据库中插入2024年6月28日的模拟买入信号")
    print("   - 设置策略状态为激活期")
    print("   - 设置起始期日期和价格")
    print("")
    print("3. 策略将直接进入激活期，开始执行价值平均策略")
    print("")
    print("优点：")
    print("✓ 配置简单，客户只需修改几个参数")
    print("✓ 自动处理所有必要的数据库记录")
    print("✓ 完全模拟真实的信号触发过程")
    print("✓ 可追溯，有完整的信号历史记录")


def example_2_manual_database_insert():
    """
    示例2：手动插入数据库记录
    适合需要精确控制的场景
    """
    print("=" * 60)
    print("示例2：手动插入数据库记录")
    print("=" * 60)
    
    try:
        # 初始化数据库
        init_database()
        load_strategy_status()
        
        print("当前策略状态：", g_strategy_status['current_phase'])
        
        # 手动插入模拟买入信号
        success = insert_simulated_buy_signal()
        if success:
            print("✓ 模拟买入信号插入成功")
            
            # 手动设置策略状态
            success = manual_set_active_status("2024-06-28", 1.234)
            if success:
                print("✓ 策略状态设置为激活期成功")
                print(f"新的策略状态：{g_strategy_status['current_phase']}")
                print(f"起始日期：{g_strategy_status['start_period_date']}")
                print(f"起始价格：{g_strategy_status['start_period_price']}")
            else:
                print("✗ 策略状态设置失败")
        else:
            print("✗ 模拟买入信号插入失败")
            
    except Exception as e:
        print(f"示例执行失败：{str(e)}")


def example_3_query_current_status():
    """
    示例3：查询当前策略状态
    """
    print("=" * 60)
    print("示例3：查询当前策略状态")
    print("=" * 60)
    
    try:
        # 初始化数据库
        init_database()
        load_strategy_status()
        
        print("策略状态详情：")
        print(f"  当前阶段：{g_strategy_status['current_phase']}")
        print(f"  最后检测时间：{g_strategy_status['last_check_time']}")
        
        if g_strategy_status['current_phase'] == 'active':
            print(f"  首次激活时间：{g_strategy_status['first_activation_time']}")
            print(f"  起始期日期：{g_strategy_status['start_period_date']}")
            print(f"  起始期价格：{g_strategy_status['start_period_price']}")
            print(f"  当前期数：{g_strategy_status['current_period']}")
            
            # 查询信号历史
            cursor = g_db_connection.cursor()
            cursor.execute("""
                SELECT signal_date, signal_type, signal_price, is_valid 
                FROM signal_history 
                WHERE signal_type = 'ENTERLONG' AND is_valid = 1
                ORDER BY signal_date DESC 
                LIMIT 5
            """)
            
            signals = cursor.fetchall()
            if signals:
                print("\n最近的买入信号记录：")
                for signal in signals:
                    print(f"  {signal[0]} - {signal[1]} - 价格：{signal[2]:.4f}")
            else:
                print("\n未找到买入信号记录")
        else:
            print("  策略当前处于沉睡期")
            
    except Exception as e:
        print(f"查询失败：{str(e)}")


def example_4_reset_to_sleeping():
    """
    示例4：重置策略到沉睡期
    """
    print("=" * 60)
    print("示例4：重置策略到沉睡期")
    print("=" * 60)
    
    try:
        # 初始化数据库
        init_database()
        load_strategy_status()
        
        print(f"当前状态：{g_strategy_status['current_phase']}")
        
        # 重置策略状态
        from value_averaging_strategy import reset_strategy_state
        reset_strategy_state()
        
        print(f"重置后状态：{g_strategy_status['current_phase']}")
        print("✓ 策略已重置为沉睡期")
        
    except Exception as e:
        print(f"重置失败：{str(e)}")


def show_configuration_guide():
    """
    显示配置指南
    """
    print("=" * 60)
    print("客户部署配置指南")
    print("=" * 60)
    
    print("步骤1：修改配置参数")
    print("在 value_averaging_strategy.py 文件中找到以下配置：")
    print("")
    print("# 强制激活期配置（客户部署专用）")
    print("FORCE_ACTIVE_MODE = True              # 改为 True")
    print("FORCE_ACTIVE_START_DATE = '2024-06-28'  # 设置历史最高点日期")
    print("FORCE_ACTIVE_START_PRICE = 1.234        # 设置历史最高点价格")
    print("")
    
    print("步骤2：运行策略")
    print("正常启动策略，系统会自动检测强制激活模式并：")
    print("- 插入模拟的2024年6月买入信号")
    print("- 设置策略为激活期")
    print("- 开始执行价值平均策略")
    print("")
    
    print("步骤3：验证状态")
    print("在策略初始化日志中查看：")
    print("✓ 策略强制激活成功")
    print("当前策略状态：")
    print("  阶段：active")
    print("  强制激活模式：开启")
    print("")
    
    print("注意事项：")
    print("⚠️  强制激活模式只在策略首次运行或从沉睡期切换时生效")
    print("⚠️  如果策略已经是激活期，不会重复激活")
    print("⚠️  建议在生产环境部署前先在测试环境验证")


if __name__ == "__main__":
    print("强制激活模式使用示例")
    print("适用于让策略直接进入激活期的客户部署场景")
    print("")
    
    while True:
        print("\n请选择示例：")
        print("1. 配置参数强制激活模式（推荐）")
        print("2. 手动插入数据库记录")
        print("3. 查询当前策略状态")
        print("4. 重置策略到沉睡期")
        print("5. 显示配置指南")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == "1":
            example_1_force_active_mode()
        elif choice == "2":
            example_2_manual_database_insert()
        elif choice == "3":
            example_3_query_current_status()
        elif choice == "4":
            example_4_reset_to_sleeping()
        elif choice == "5":
            show_configuration_guide()
        elif choice == "0":
            print("退出程序")
            break
        else:
            print("无效选择，请重新输入")
