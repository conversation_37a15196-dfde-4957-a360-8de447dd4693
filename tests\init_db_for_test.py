# -*- coding: utf-8 -*-
"""
Initialize database for testing force active mode
"""

import sqlite3
import datetime

def init_database():
    """Initialize SQLite database with required tables"""
    DATABASE_PATH = "gytrading2.db"
    
    try:
        # Connect to database
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        print("Initializing database...")
        
        # Create strategy_status table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS strategy_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                current_phase TEXT NOT NULL,           -- 'sleeping' or 'active'
                last_check_time TEXT NOT NULL,         -- Last check time
                first_activation_time TEXT,            -- First activation time
                start_period_date TEXT,                -- Start period date
                start_period_price REAL,              -- Start period price
                current_period INTEGER DEFAULT 0,      -- Current period
                last_adjustment_period INTEGER DEFAULT 0, -- Last adjustment period
                last_adjustment_time TEXT,             -- Last adjustment time
                created_time TEXT NOT NULL,           -- Created time
                updated_time TEXT NOT NULL            -- Updated time
            )
        """)
        
        # Create signal_history table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS signal_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                signal_date TEXT NOT NULL,            -- Signal date
                signal_type TEXT NOT NULL,            -- 'ENTERLONG' or 'EXITLONG'
                signal_price REAL NOT NULL,           -- Signal price
                ema_value REAL NOT NULL,              -- EMA value
                bottom_line REAL,                     -- Bottom line F1
                top_line REAL,                        -- Top line F2
                kline_position INTEGER,               -- K-line position
                kline_date TEXT,                      -- K-line date (YYYYMMDD)
                is_valid INTEGER NOT NULL,            -- Valid signal: 1=valid, 0=invalid
                filter_reason TEXT,                   -- Filter reason
                created_time TEXT NOT NULL           -- Created time
            )
        """)
        
        # Create other required tables (simplified versions)
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_date TEXT NOT NULL,
                stock_code TEXT NOT NULL,
                order_type TEXT NOT NULL,
                order_reason TEXT NOT NULL,
                target_amount REAL,
                target_shares INTEGER,
                actual_shares INTEGER,
                actual_price REAL,
                order_status TEXT NOT NULL,
                retry_count INTEGER DEFAULT 0,
                error_message TEXT,
                execution_time TEXT,
                created_time TEXT NOT NULL
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                log_date TEXT NOT NULL,
                kline_date TEXT,
                log_type TEXT NOT NULL,
                operation TEXT NOT NULL,
                message TEXT NOT NULL,
                details TEXT,
                is_backtest INTEGER DEFAULT 0,
                created_time TEXT NOT NULL
            )
        """)
        
        # Check if strategy_status has initial record
        cursor.execute("SELECT COUNT(*) FROM strategy_status")
        if cursor.fetchone()[0] == 0:
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            cursor.execute("""
                INSERT INTO strategy_status
                (current_phase, last_check_time, created_time, updated_time)
                VALUES ('sleeping', ?, ?, ?)
            """, (current_time, current_time, current_time))
        
        conn.commit()
        conn.close()
        
        print("✓ Database initialized successfully")
        return True
        
    except Exception as e:
        print(f"✗ Database initialization failed: {str(e)}")
        return False


if __name__ == "__main__":
    init_database()
