# 任务拆分和重试机制实现说明

## 功能概述

本次实现了两个核心功能：
1. **任务自动拆分**：当单次交易股数超过`MAX_TRADE_SHARES`时，自动拆分为多个任务
2. **智能重试机制**：失败任务自动重试，并智能处理同类型/不同类型任务的依赖关系

## 1. 任务拆分功能

### 核心逻辑
- 当`target_shares > MAX_TRADE_SHARES`时，自动拆分为多个任务
- 拆分数量：`math.ceil(target_shares / MAX_TRADE_SHARES)`
- 拆分任务按顺序执行：任务2依赖任务1，任务3依赖任务2，以此类推

### 拆分示例
```
目标股数：2,500,000股
MAX_TRADE_SHARES：1,000,000股

拆分结果：
- 任务1：1,000,000股（无依赖）
- 任务2：1,000,000股（依赖任务1）
- 任务3：500,000股（依赖任务2）
```

### 实现方法
- `create_task_with_split()`：新的拆分方法，返回任务列表
- `create_task()`：保持向后兼容，返回第一个任务信息
- `create_single_task()`：内部方法，创建单个任务

## 2. 重试机制

### 重试计数逻辑
```
任务创建：retry_count = -1
首次失败：retry_count = 0（还有3次机会）
第二次失败：retry_count = 1（还有2次机会）
第三次失败：retry_count = 2（还有1次机会）
第四次失败：retry_count = 3（彻底失败）
```

### 重试条件
- 任务状态为`FAILED`
- `retry_count < MAX_RETRY_COUNT`（默认3）
- 距离上次失败超过`RETRY_INTERVAL`秒（默认30秒）
- 依赖任务已满足执行条件

### 失败处理策略

#### 同类型任务（可继续执行）
- **买买关系**：`BUY_159915_CASH` → `BUY_159915_MARGIN`
- **卖卖关系**：`SELL_510720` → `SELL_159915`
- 前一个任务失败不影响后续同类型任务执行

#### 不同类型任务（需要取消）
- **卖买关系**：`SELL_510720` → `BUY_159915_CASH`（买入需要卖出的钱）
- **买卖关系**：`BUY_159915_CASH` → `SELL_159915`（卖出需要买入的股票）
- 前一个任务失败会取消所有后续不同类型任务

## 3. 依赖逻辑增强

### 新的依赖条件
任务可以执行的条件：
```sql
depends_on_task IS NULL 
OR EXISTS (
    SELECT 1 FROM trade_task_queue t2
    WHERE t2.id = CAST(t1.depends_on_task AS INTEGER)
    AND (t2.task_status = 'COMPLETED' 
         OR (t2.task_status = 'FAILED' AND t2.retry_count >= 3))
)
```

### 依赖状态说明
- `COMPLETED`：依赖任务成功完成
- `FAILED + retry_count >= 3`：依赖任务彻底失败，但如果是同类型任务可继续执行

## 4. 数据库变更

### 表结构修改
```sql
-- 为trade_task_queue表添加retry_count字段
ALTER TABLE trade_task_queue 
ADD COLUMN retry_count INTEGER DEFAULT -1;

-- 更新现有任务的retry_count
UPDATE trade_task_queue 
SET retry_count = -1 
WHERE retry_count IS NULL;
```

### 字段说明
- `retry_count = -1`：任务创建时的初始状态
- `retry_count = 0,1,2`：失败重试阶段
- `retry_count >= 3`：彻底失败状态

## 5. 关键实现方法

### TradeTaskQueue类
- `create_task_with_split()`：支持拆分的任务创建
- `create_task()`：向后兼容的任务创建接口

### TradeTaskExecutor类
- `get_next_executable_task()`：查找可执行任务（包含新依赖逻辑）
- `get_next_retry_task()`：查找需要重试的任务
- `increment_retry_count()`：更新重试计数
- `handle_final_task_failure()`：处理最终失败任务
- `is_buy_task()` / `is_sell_task()`：任务类型判断
- `cancel_dependent_tasks()`：递归取消依赖任务

### TradeTaskCallbackHandler类
- `process_order_callback_with_uuid()`：增强的回调处理，自动更新retry_count

## 6. 使用说明

### 配置参数
```python
MAX_TRADE_SHARES = 1000000      # 单次最大交易股数
MAX_RETRY_COUNT = 3             # 最大重试次数
RETRY_INTERVAL = 30             # 重试间隔（秒）
```

### 使用方式
现有代码无需修改，系统会自动：
1. 检查是否需要拆分任务
2. 处理失败任务的重试
3. 智能处理任务依赖关系

### 日志监控
系统会记录详细的日志，包括：
- `TASK_SPLIT`：任务拆分日志
- `TASK_RETRY`：重试日志
- `TASK_FAILURE_HANDLING`：失败处理日志
- `CALLBACK_FINAL_FAILURE`：回调最终失败日志

## 7. 测试验证

所有核心逻辑已通过单元测试验证：
- ✅ 拆分逻辑正确
- ✅ 重试计数逻辑正确
- ✅ 依赖关系处理正确
- ✅ 同类型/不同类型任务判断正确

## 8. 注意事项

1. **向后兼容**：现有调用`create_task()`的代码无需修改
2. **数据库迁移**：首次运行时会自动添加`retry_count`字段
3. **性能影响**：拆分任务会增加数据库记录，但不影响执行效率
4. **监控建议**：建议监控任务拆分和重试的日志，及时发现问题

## 9. 配置建议

根据实际交易需求调整参数：
- 大额交易：降低`MAX_TRADE_SHARES`以减少单笔风险
- 网络不稳定：增加`MAX_RETRY_COUNT`和`RETRY_INTERVAL`
- 高频交易：提高`MAX_TRADE_SHARES`以减少拆分开销
