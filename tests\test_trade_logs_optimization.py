# -*- coding: utf-8 -*-
"""
测试trade_logs表优化效果
验证K线日期记录和查询功能
"""

import datetime
import sqlite3
import json

class MockContextInfo:
    """模拟iQuant的ContextInfo对象"""
    
    def __init__(self, backtest_time=None, is_backtest=True):
        self.barpos = 100
        self.run_count = 1
        self.accountid = "TEST_ACCOUNT"
        self.stock = "159915.SZ"
        self.accID = "TEST_ACCOUNT"
        self.backtest_time = backtest_time or datetime.datetime(2024, 8, 30)
        self._is_backtest = is_backtest
        
    def get_bar_timetag(self, barpos):
        """模拟获取K线时间戳"""
        return int(self.backtest_time.timestamp() * 1000)


def test_trade_logs_optimization():
    """测试trade_logs表优化效果"""
    print("=== 测试trade_logs表优化效果 ===")
    
    # 创建内存数据库进行测试
    db_connection = sqlite3.connect(":memory:")
    
    try:
        # 创建优化后的trade_logs表
        cursor = db_connection.cursor()
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                log_date TEXT NOT NULL,               -- 日志日期（系统时间）
                kline_date TEXT,                      -- K线日期（回测时使用）
                log_type TEXT NOT NULL,               -- 日志类型：'INFO', 'WARNING', 'ERROR'
                operation TEXT NOT NULL,              -- 操作类型
                message TEXT NOT NULL,                -- 日志消息
                details TEXT,                         -- 详细信息（JSON格式）
                is_backtest INTEGER DEFAULT 0,       -- 是否回测模式（0=实盘，1=回测）
                created_time TEXT NOT NULL           -- 创建时间
            )
        """)
        
        # 模拟全局变量
        global g_db_connection, g_current_bar_time
        g_db_connection = db_connection
        g_current_bar_time = datetime.datetime(2024, 8, 30)
        
        def is_backtest_mode(ContextInfo=None):
            """模拟回测模式检查"""
            return ContextInfo._is_backtest if ContextInfo else False
        
        def get_current_time(ContextInfo):
            """模拟获取当前时间（回测适配）"""
            if ContextInfo and hasattr(ContextInfo, 'backtest_time'):
                return ContextInfo.backtest_time
            return g_current_bar_time
        
        def log_message_test(log_type, operation, message, details=None, ContextInfo=None):
            """测试优化后的log_message函数"""
            try:
                if g_db_connection is None:
                    print(f"[{log_type}] {operation}: {message}")
                    return

                cursor = g_db_connection.cursor()
                current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                details_json = json.dumps(details, ensure_ascii=False) if details else None
                
                # 获取K线日期和回测模式标识
                kline_date = None
                is_backtest = 0
                
                if ContextInfo:
                    try:
                        is_backtest = 1 if is_backtest_mode(ContextInfo) else 0
                        if is_backtest:
                            # 回测模式：使用当前K线时间
                            kline_time = get_current_time(ContextInfo)
                            kline_date = kline_time.strftime("%Y-%m-%d %H:%M:%S")
                        else:
                            # 实盘模式：K线日期与系统时间相同
                            kline_date = current_time
                    except Exception as e:
                        print(f"获取K线时间失败：{str(e)}")
                        kline_date = current_time

                cursor.execute("""
                    INSERT INTO trade_logs (log_date, kline_date, log_type, operation, message, details, is_backtest, created_time)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (current_time, kline_date, log_type, operation, message, details_json, is_backtest, current_time))

                g_db_connection.commit()

            except Exception as e:
                print(f"记录日志失败：{str(e)}")
        
        # 测试场景1：回测模式日志
        print("\n--- 场景1：回测模式日志记录 ---")
        backtest_context = MockContextInfo(datetime.datetime(2024, 8, 30), True)
        
        log_message_test("INFO", "价值平均", "开始执行价值平均策略", 
                        {"period": 8, "target_amount": 80000}, backtest_context)
        log_message_test("INFO", "价值平均买入", "需要买入1000股", 
                        {"shares": 1000, "price": 2.5}, backtest_context)
        
        # 测试场景2：实盘模式日志
        print("--- 场景2：实盘模式日志记录 ---")
        realtime_context = MockContextInfo(datetime.datetime.now(), False)
        
        log_message_test("INFO", "阶段切换", "从sleeping切换到active", 
                        {"from": "sleeping", "to": "active"}, realtime_context)
        
        # 测试场景3：无ContextInfo的日志
        print("--- 场景3：无ContextInfo的日志记录 ---")
        log_message_test("WARNING", "系统", "无ContextInfo的日志记录")
        
        # 查询并显示结果
        print("\n--- 查询结果 ---")
        cursor.execute("""
            SELECT 
                id, log_date, kline_date, log_type, operation, message, is_backtest
            FROM trade_logs 
            ORDER BY id
        """)
        
        results = cursor.fetchall()
        
        print(f"总共记录了 {len(results)} 条日志：")
        print(f"{'ID':<3} {'系统时间':<20} {'K线时间':<20} {'类型':<8} {'操作':<12} {'回测':<4} {'消息'}")
        print("-" * 100)
        
        for row in results:
            id_, log_date, kline_date, log_type, operation, message, is_backtest = row
            backtest_flag = "是" if is_backtest else "否"
            kline_display = kline_date or "N/A"
            print(f"{id_:<3} {log_date:<20} {kline_display:<20} {log_type:<8} {operation:<12} {backtest_flag:<4} {message}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False
    finally:
        db_connection.close()


def test_query_functions():
    """测试查询函数"""
    print("\n=== 测试查询函数 ===")
    
    # 创建内存数据库并插入测试数据
    db_connection = sqlite3.connect(":memory:")
    
    try:
        # 创建表
        cursor = db_connection.cursor()
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trade_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                log_date TEXT NOT NULL,
                kline_date TEXT,
                log_type TEXT NOT NULL,
                operation TEXT NOT NULL,
                message TEXT NOT NULL,
                details TEXT,
                is_backtest INTEGER DEFAULT 0,
                created_time TEXT NOT NULL
            )
        """)
        
        # 插入测试数据
        test_data = [
            ("2025-08-13 10:00:00", "2024-08-30 09:30:00", "INFO", "价值平均", "执行价值平均策略", None, 1, "2025-08-13 10:00:00"),
            ("2025-08-13 10:01:00", "2024-08-30 09:31:00", "INFO", "价值平均买入", "买入1000股", '{"shares": 1000}', 1, "2025-08-13 10:01:00"),
            ("2025-08-13 10:02:00", "2024-09-30 09:30:00", "INFO", "价值平均", "执行价值平均策略", None, 1, "2025-08-13 10:02:00"),
            ("2025-08-13 10:03:00", "2025-08-13 10:03:00", "INFO", "阶段切换", "切换到激活期", None, 0, "2025-08-13 10:03:00"),
        ]
        
        cursor.executemany("""
            INSERT INTO trade_logs (log_date, kline_date, log_type, operation, message, details, is_backtest, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, test_data)
        
        db_connection.commit()
        
        # 模拟全局变量
        global g_db_connection
        g_db_connection = db_connection
        
        def query_trade_logs_by_kline_date_test(start_date=None, end_date=None, operation=None, log_type=None):
            """测试查询函数"""
            try:
                if g_db_connection is None:
                    print("数据库连接为空")
                    return []
                
                cursor = g_db_connection.cursor()
                
                # 构建查询条件
                conditions = []
                params = []
                
                if start_date:
                    conditions.append("DATE(kline_date) >= ?")
                    params.append(start_date)
                    
                if end_date:
                    conditions.append("DATE(kline_date) <= ?")
                    params.append(end_date)
                    
                if operation:
                    conditions.append("operation = ?")
                    params.append(operation)
                    
                if log_type:
                    conditions.append("log_type = ?")
                    params.append(log_type)
                
                where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""
                
                query = f"""
                    SELECT 
                        id, log_date, kline_date, log_type, operation, message, details, is_backtest, created_time
                    FROM trade_logs
                    {where_clause}
                    ORDER BY kline_date DESC, id DESC
                """
                
                cursor.execute(query, params)
                results = cursor.fetchall()
                
                # 转换为字典格式便于查看
                columns = ['id', 'log_date', 'kline_date', 'log_type', 'operation', 'message', 'details', 'is_backtest', 'created_time']
                return [dict(zip(columns, row)) for row in results]
                
            except Exception as e:
                print(f"查询交易日志失败：{str(e)}")
                return []
        
        # 测试不同的查询条件
        print("\n--- 查询2024年8月的日志 ---")
        results = query_trade_logs_by_kline_date_test("2024-08-01", "2024-08-31")
        print(f"找到 {len(results)} 条记录")
        for r in results:
            print(f"  {r['kline_date']} - {r['operation']}: {r['message']}")
        
        print("\n--- 查询价值平均相关的日志 ---")
        results = query_trade_logs_by_kline_date_test(operation="价值平均")
        print(f"找到 {len(results)} 条记录")
        for r in results:
            print(f"  {r['kline_date']} - {r['message']}")
        
        print("\n--- 查询2024年9月的日志 ---")
        results = query_trade_logs_by_kline_date_test("2024-09-01", "2024-09-30")
        print(f"找到 {len(results)} 条记录")
        for r in results:
            print(f"  {r['kline_date']} - {r['operation']}: {r['message']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 查询函数测试失败: {str(e)}")
        return False
    finally:
        db_connection.close()


def main():
    """主测试函数"""
    print("开始测试trade_logs表优化效果...")
    
    tests = [
        test_trade_logs_optimization,
        test_query_functions
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 发生异常: {str(e)}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 trade_logs表优化测试通过！")
        print("\n优化要点:")
        print("1. ✅ 新增kline_date字段记录K线日期")
        print("2. ✅ 新增is_backtest字段区分回测/实盘")
        print("3. ✅ log_message函数支持ContextInfo参数")
        print("4. ✅ 提供便捷的查询函数")
        
        print("\n使用建议:")
        print("- 查询回测日志：WHERE is_backtest = 1")
        print("- 按K线日期查询：WHERE DATE(kline_date) = '2024-08-30'")
        print("- 查询特定操作：WHERE operation = '价值平均'")
        print("- 使用query_trade_logs_by_kline_date()函数进行复合查询")
    else:
        print("❌ 部分测试失败，需要进一步调试")
    
    return passed == total


if __name__ == "__main__":
    main()
