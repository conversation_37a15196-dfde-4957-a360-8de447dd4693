# -*- coding: utf-8 -*-
"""
简单的任务拆分逻辑测试
"""

import math

# 模拟参数
MAX_TRADE_SHARES = 1000000

def test_split_logic():
    """测试拆分逻辑"""
    print("=" * 50)
    print("测试任务拆分逻辑")
    print("=" * 50)
    
    test_cases = [
        (500, 1),           # 不需要拆分
        (1000000, 1),       # 刚好等于最大值
        (1000001, 2),       # 超过1股，需要拆分为2个
        (1500000, 2),       # 1.5倍，拆分为2个
        (2000000, 2),       # 2倍，拆分为2个
        (2000001, 3),       # 超过2倍，拆分为3个
        (3500000, 4),       # 3.5倍，拆分为4个
    ]
    
    for target_shares, expected_splits in test_cases:
        print(f"\n测试：{target_shares:,}股")
        
        if target_shares <= MAX_TRADE_SHARES:
            actual_splits = 1
            print(f"  不需要拆分：{actual_splits}个任务")
        else:
            actual_splits = math.ceil(target_shares / MAX_TRADE_SHARES)
            print(f"  需要拆分：{actual_splits}个任务")
            
            # 模拟拆分过程
            remaining_shares = target_shares
            split_index = 1
            
            while remaining_shares > 0:
                current_batch_shares = min(remaining_shares, MAX_TRADE_SHARES)
                print(f"    第{split_index}个任务：{current_batch_shares:,}股")
                remaining_shares -= current_batch_shares
                split_index += 1
        
        assert actual_splits == expected_splits, f"预期{expected_splits}个任务，实际{actual_splits}个"
        print(f"  ✓ 测试通过")
    
    print("\n" + "=" * 50)
    print("所有拆分逻辑测试通过！")
    print("=" * 50)

def test_dependency_chain():
    """测试依赖链逻辑"""
    print("=" * 50)
    print("测试依赖链逻辑")
    print("=" * 50)
    
    target_shares = 2500000  # 需要拆分为3个任务
    initial_depends_on = "task_100"  # 初始依赖任务
    
    print(f"目标股数：{target_shares:,}")
    print(f"初始依赖任务：{initial_depends_on}")
    print(f"最大单次股数：{MAX_TRADE_SHARES:,}")
    
    # 模拟拆分过程
    split_tasks = []
    remaining_shares = target_shares
    current_depends_on = initial_depends_on
    split_index = 1
    
    while remaining_shares > 0:
        current_batch_shares = min(remaining_shares, MAX_TRADE_SHARES)
        
        # 模拟任务ID
        task_id = f"task_{1000 + split_index}"
        
        split_tasks.append({
            'task_id': task_id,
            'shares': current_batch_shares,
            'depends_on': current_depends_on,
            'split_index': split_index
        })
        
        print(f"第{split_index}个任务：")
        print(f"  任务ID：{task_id}")
        print(f"  股数：{current_batch_shares:,}")
        print(f"  依赖：{current_depends_on}")
        
        # 下一个任务依赖于当前任务
        current_depends_on = task_id
        remaining_shares -= current_batch_shares
        split_index += 1
    
    print(f"\n总共创建{len(split_tasks)}个任务")
    
    # 验证依赖链
    for i, task in enumerate(split_tasks):
        if i == 0:
            # 第一个任务应该依赖初始任务
            assert task['depends_on'] == initial_depends_on, f"第一个任务应该依赖{initial_depends_on}"
        else:
            # 后续任务应该依赖前一个任务
            prev_task_id = split_tasks[i-1]['task_id']
            assert task['depends_on'] == prev_task_id, f"第{i+1}个任务应该依赖{prev_task_id}"
    
    print("✓ 依赖链测试通过")
    
    print("\n" + "=" * 50)
    print("依赖链逻辑测试通过！")
    print("=" * 50)

if __name__ == "__main__":
    print("开始测试任务拆分逻辑...")
    
    try:
        test_split_logic()
        test_dependency_chain()
        print("\n🎉 所有逻辑测试通过！")
    except Exception as e:
        print(f"\n❌ 测试失败：{str(e)}")
        import traceback
        traceback.print_exc()
