# -*- coding: utf-8 -*-
"""
测试新的激进策略信号检测逻辑
"""

def test_signal_detection_logic():
    """测试信号检测逻辑"""
    print("=" * 70)
    print("测试激进策略新的信号检测逻辑")
    print("=" * 70)
    
    # 模拟不同的历史信号情况
    test_scenarios = [
        {
            'name': '没有历史信号',
            'latest_signal': None,
            'expected_buy': False,
            'expected_sell': False,
            'description': '数据库中没有历史信号记录'
        },
        {
            'name': '最近一次是买入信号',
            'latest_signal': {
                'signal_type': 'ENTERLONG',
                'signal_price': 1.234,
                'signal_date': '2024-08-30'
            },
            'expected_buy': True,
            'expected_sell': False,
            'description': '当前应该处于激活期，执行激活期操作'
        },
        {
            'name': '最近一次是卖出信号',
            'latest_signal': {
                'signal_type': 'EXITLONG',
                'signal_price': 2.567,
                'signal_date': '2024-09-01'
            },
            'expected_buy': False,
            'expected_sell': True,
            'description': '当前应该处于沉睡期，执行沉睡期操作'
        },
        {
            'name': '未知信号类型',
            'latest_signal': {
                'signal_type': 'UNKNOWN',
                'signal_price': 1.500,
                'signal_date': '2024-08-25'
            },
            'expected_buy': False,
            'expected_sell': False,
            'description': '未知信号类型，不执行操作'
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n{scenario['name']}:")
        print(f"  {scenario['description']}")
        
        latest_signal = scenario['latest_signal']
        
        # 模拟信号检测逻辑
        if latest_signal is None:
            # 没有历史信号
            has_buy_signal = False
            has_sell_signal = False
            signal_reason = "数据库中没有历史信号记录"
        else:
            signal_type = latest_signal['signal_type']
            if signal_type == 'ENTERLONG':
                # 最近一次是买入信号，当前应该处于激活期
                has_buy_signal = True
                has_sell_signal = False
                signal_reason = "根据最近一次买入信号，当前应处于激活期"
            elif signal_type == 'EXITLONG':
                # 最近一次是卖出信号，当前应该处于沉睡期
                has_buy_signal = False
                has_sell_signal = True
                signal_reason = "根据最近一次卖出信号，当前应处于沉睡期"
            else:
                # 未知信号类型
                has_buy_signal = False
                has_sell_signal = False
                signal_reason = f"未知的信号类型：{signal_type}"
        
        print(f"  历史信号：{latest_signal}")
        print(f"  检测结果：买入={has_buy_signal}, 卖出={has_sell_signal}")
        print(f"  原因：{signal_reason}")
        print(f"  预期：买入={scenario['expected_buy']}, 卖出={scenario['expected_sell']}")
        
        # 验证结果
        assert has_buy_signal == scenario['expected_buy'], "买入信号判断错误"
        assert has_sell_signal == scenario['expected_sell'], "卖出信号判断错误"
        
        print(f"  ✓ 验证通过")
    
    print("\n" + "=" * 70)
    print("信号检测逻辑验证通过！")
    print("=" * 70)

def test_trading_execution_flow():
    """测试交易执行流程"""
    print("=" * 70)
    print("测试交易执行流程")
    print("=" * 70)
    
    # 模拟不同的信号状态转换
    execution_scenarios = [
        {
            'name': '无信号 → 买入信号',
            'previous_signal': None,
            'current_buy': True,
            'current_sell': False,
            'expected_action': 'execute_aggressive_buy_active',
            'description': '一次性按照总投资金额买入ACTIVE_FUND_CODE'
        },
        {
            'name': '卖出信号 → 买入信号',
            'previous_signal': 'EXITLONG',
            'current_buy': True,
            'current_sell': False,
            'expected_action': 'execute_aggressive_sell_sleeping_buy_active',
            'description': '卖出SLEEPING_FUND_CODE，买入ACTIVE_FUND_CODE'
        },
        {
            'name': '买入信号 → 买入信号',
            'previous_signal': 'ENTERLONG',
            'current_buy': True,
            'current_sell': False,
            'expected_action': 'no_action',
            'description': '已在激活期，无需操作'
        },
        {
            'name': '无信号 → 卖出信号',
            'previous_signal': None,
            'current_buy': False,
            'current_sell': True,
            'expected_action': 'execute_aggressive_buy_sleeping',
            'description': '买入SLEEPING_FUND_CODE（仅现金）'
        },
        {
            'name': '买入信号 → 卖出信号',
            'previous_signal': 'ENTERLONG',
            'current_buy': False,
            'current_sell': True,
            'expected_action': 'execute_aggressive_sell_active_buy_sleeping',
            'description': '卖出ACTIVE_FUND_CODE，买入SLEEPING_FUND_CODE'
        },
        {
            'name': '卖出信号 → 卖出信号',
            'previous_signal': 'EXITLONG',
            'current_buy': False,
            'current_sell': True,
            'expected_action': 'no_action',
            'description': '已在沉睡期，无需操作'
        }
    ]
    
    for scenario in execution_scenarios:
        print(f"\n{scenario['name']}:")
        print(f"  {scenario['description']}")
        
        previous_signal = scenario['previous_signal']
        current_buy = scenario['current_buy']
        current_sell = scenario['current_sell']
        expected_action = scenario['expected_action']
        
        # 模拟交易执行逻辑
        if previous_signal is None and current_buy:
            actual_action = 'execute_aggressive_buy_active'
        elif previous_signal == 'EXITLONG' and current_buy:
            actual_action = 'execute_aggressive_sell_sleeping_buy_active'
        elif previous_signal == 'ENTERLONG' and current_buy:
            actual_action = 'no_action'
        elif previous_signal is None and current_sell:
            actual_action = 'execute_aggressive_buy_sleeping'
        elif previous_signal == 'ENTERLONG' and current_sell:
            actual_action = 'execute_aggressive_sell_active_buy_sleeping'
        elif previous_signal == 'EXITLONG' and current_sell:
            actual_action = 'no_action'
        else:
            actual_action = 'no_action'
        
        print(f"  上次信号：{previous_signal}")
        print(f"  当前信号：买入={current_buy}, 卖出={current_sell}")
        print(f"  预期操作：{expected_action}")
        print(f"  实际操作：{actual_action}")
        
        assert actual_action == expected_action, f"交易执行逻辑错误"
        print(f"  ✓ 验证通过")
    
    print("\n" + "=" * 70)
    print("交易执行流程验证通过！")
    print("=" * 70)

def test_complete_workflow():
    """测试完整工作流程"""
    print("=" * 70)
    print("测试完整工作流程")
    print("=" * 70)
    
    print("激进策略完整工作流程：")
    print("1. 信号检测：查询数据库中最近一次EMA信号")
    print("2. 阶段判断：根据信号类型判断当前应处于什么阶段")
    print("3. 防重复检查：检查当天和当前阶段是否已交易")
    print("4. 状态转换：根据信号变化执行相应的一次性交易")
    print("5. 任务创建：创建异步交易任务")
    
    print(f"\n关键差异对比：")
    print(f"稳健策略：")
    print(f"  - 实时检测当前K线的EMA穿越")
    print(f"  - 发现新信号时执行阶段切换")
    print(f"  - 激活期执行价值平均策略（持续调整）")
    
    print(f"激进策略：")
    print(f"  - 查询历史上最近一次EMA信号")
    print(f"  - 根据历史信号判断当前应处于什么阶段")
    print(f"  - 每个阶段只执行一次性交易")
    
    print(f"\n可能的阻止因素：")
    print(f"  1. 时点控制：TRADE_TIME_CONTROL设置")
    print(f"  2. 防重复机制：当天已有交易记录")
    print(f"  3. 期间交易检查：当前阶段已经交易过")
    print(f"  4. 账户状态：账户未登录或资金不足")
    
    print("\n" + "=" * 70)
    print("完整工作流程分析完成！")
    print("=" * 70)

if __name__ == "__main__":
    print("开始测试激进策略新的信号检测逻辑...")
    
    try:
        test_signal_detection_logic()
        test_trading_execution_flow()
        test_complete_workflow()
        print("\n🎉 激进策略信号检测逻辑测试通过！")
        
        print("\n" + "=" * 70)
        print("总结")
        print("=" * 70)
        print("✅ 信号检测逻辑已修改为查询历史信号")
        print("✅ 根据最近一次信号判断当前应处于的阶段")
        print("✅ 交易执行逻辑保持不变（已经正确）")
        print("⚠️  仍需检查其他可能的阻止因素")
        
    except Exception as e:
        print(f"\n❌ 测试失败：{str(e)}")
        import traceback
        traceback.print_exc()
