# -*- coding: utf-8 -*-
"""
验证拆分逻辑的正确性
"""

import math

def verify_split_logic():
    """验证拆分逻辑"""
    MAX_TRADE_SHARES = 1000000
    
    test_cases = [
        (500, 1, [500]),
        (1000000, 1, [1000000]),
        (1000001, 2, [1000000, 1]),
        (1500000, 2, [1000000, 500000]),
        (2000000, 2, [1000000, 1000000]),
        (2000001, 3, [1000000, 1000000, 1]),
        (3500000, 4, [1000000, 1000000, 1000000, 500000]),
    ]
    
    print("验证任务拆分逻辑：")
    print(f"MAX_TRADE_SHARES = {MAX_TRADE_SHARES:,}")
    print("-" * 60)
    
    for target_shares, expected_splits, expected_distribution in test_cases:
        print(f"\n目标股数：{target_shares:,}")
        
        if target_shares <= MAX_TRADE_SHARES:
            actual_splits = 1
            actual_distribution = [target_shares]
        else:
            actual_splits = math.ceil(target_shares / MAX_TRADE_SHARES)
            
            # 计算实际分配
            actual_distribution = []
            remaining_shares = target_shares
            
            while remaining_shares > 0:
                current_batch = min(remaining_shares, MAX_TRADE_SHARES)
                actual_distribution.append(current_batch)
                remaining_shares -= current_batch
        
        print(f"预期拆分：{expected_splits}个任务 {expected_distribution}")
        print(f"实际拆分：{actual_splits}个任务 {actual_distribution}")
        
        assert actual_splits == expected_splits, f"拆分数量不匹配"
        assert actual_distribution == expected_distribution, f"股数分配不匹配"
        
        print("✓ 通过")
    
    print("\n" + "=" * 60)
    print("所有拆分逻辑验证通过！")
    print("=" * 60)

def verify_dependency_logic():
    """验证依赖逻辑"""
    print("\n验证依赖链逻辑：")
    print("-" * 60)
    
    target_shares = 2500000
    initial_depends_on = "task_100"
    MAX_TRADE_SHARES = 1000000
    
    print(f"目标股数：{target_shares:,}")
    print(f"初始依赖：{initial_depends_on}")
    
    # 模拟依赖链创建
    dependencies = []
    remaining_shares = target_shares
    current_depends_on = initial_depends_on
    split_index = 1
    
    while remaining_shares > 0:
        current_batch_shares = min(remaining_shares, MAX_TRADE_SHARES)
        task_id = f"split_task_{split_index}"
        
        dependencies.append({
            'task_id': task_id,
            'shares': current_batch_shares,
            'depends_on': current_depends_on
        })
        
        print(f"第{split_index}个任务：{task_id}，{current_batch_shares:,}股，依赖：{current_depends_on}")
        
        current_depends_on = task_id
        remaining_shares -= current_batch_shares
        split_index += 1
    
    # 验证依赖链
    expected_dependencies = [
        {'task_id': 'split_task_1', 'shares': 1000000, 'depends_on': 'task_100'},
        {'task_id': 'split_task_2', 'shares': 1000000, 'depends_on': 'split_task_1'},
        {'task_id': 'split_task_3', 'shares': 500000, 'depends_on': 'split_task_2'},
    ]
    
    assert len(dependencies) == len(expected_dependencies), "依赖链长度不匹配"
    
    for i, (actual, expected) in enumerate(zip(dependencies, expected_dependencies)):
        assert actual['task_id'] == expected['task_id'], f"第{i+1}个任务ID不匹配"
        assert actual['shares'] == expected['shares'], f"第{i+1}个任务股数不匹配"
        assert actual['depends_on'] == expected['depends_on'], f"第{i+1}个任务依赖不匹配"
    
    print("✓ 依赖链逻辑验证通过")

if __name__ == "__main__":
    try:
        verify_split_logic()
        verify_dependency_logic()
        print("\n🎉 所有验证通过！")
    except Exception as e:
        print(f"\n❌ 验证失败：{str(e)}")
        import traceback
        traceback.print_exc()
