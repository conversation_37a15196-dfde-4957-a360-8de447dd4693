#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试回调函数修复效果
"""

import sqlite3
import datetime
import uuid
from unittest.mock import Mock

# 模拟 iQuant 环境
class MockOrderInfo:
    """模拟订单信息对象"""
    def __init__(self, order_status=57, order_id="12345", instrument_id="159915.SZ", 
                 volume_traded=0, volume_total=1000, remark=""):
        self.m_nOrderStatus = order_status  # 57 = 废单
        self.m_strOrderSysID = order_id
        self.m_strInstrumentID = instrument_id
        self.m_nVolumeTraded = volume_traded
        self.m_nVolumeTotalOriginal = volume_total
        self.m_strRemark = remark

class MockContextInfo:
    """模拟上下文信息对象"""
    pass

def test_order_status_sync():
    """测试订单状态同步功能"""
    print("🧪 测试订单状态同步功能")

    try:
        # 先初始化数据库
        print("📋 初始化数据库...")
        import sys
        sys.path.append('.')
        from value_averaging_strategy import init_database
        init_database()
        print("✓ 数据库初始化完成")

        # 连接数据库
        conn = sqlite3.connect('trading_data.db')
        cursor = conn.cursor()

        # 1. 创建测试数据
        test_uuid = str(uuid.uuid4())
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 插入测试任务
        cursor.execute("""
            INSERT INTO trade_task_queue
            (task_group_id, task_type, stock_code, target_shares, target_amount,
             estimated_price, estimated_fees, task_status, order_uuid, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (test_uuid, "BUY_159915_CASH", "159915.SZ", 1000, 2500.0,
              2.5, 5.0, "WAITING_CALLBACK", test_uuid, current_time))

        task_id = cursor.lastrowid

        # 插入对应的 trade_orders 记录
        cursor.execute("""
            INSERT INTO trade_orders
            (order_date, stock_code, order_type, order_reason, target_shares,
             order_status, created_time)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (current_time, "159915.SZ", "BUY", "VALUE_AVERAGE", 1000,
              "PENDING", current_time))

        order_id = cursor.lastrowid
        conn.commit()

        print(f"✓ 创建测试数据：任务ID={task_id}，订单ID={order_id}")

        # 2. 测试同步逻辑（不依赖实际回调处理）
        from value_averaging_strategy import TradeTaskCallbackHandler

        callback_handler = TradeTaskCallbackHandler()

        # 直接测试同步方法
        print(f"🔄 测试状态同步：任务ID={task_id}")
        callback_handler.sync_trade_orders_status(task_id, 'FAILED', '订单废单')

        # 3. 检查结果
        # 检查订单状态
        cursor.execute("SELECT order_status, error_message FROM trade_orders WHERE id = ?", (order_id,))
        order_result = cursor.fetchone()
        order_status, error_message = order_result if order_result else (None, None)

        print(f"📊 检查结果：")
        print(f"   订单状态：{order_status}")
        print(f"   错误信息：{error_message}")

        # 验证结果
        if order_status == "FAILED":
            print("✅ 测试通过：订单状态同步正常")
            return True
        else:
            print("❌ 测试失败：订单状态同步异常")
            return False

    except Exception as e:
        print(f"❌ 测试异常：{str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理测试数据
        try:
            cursor.execute("DELETE FROM trade_task_queue WHERE task_group_id = ?", (test_uuid,))
            cursor.execute("DELETE FROM trade_orders WHERE stock_code = '159915.SZ' AND order_reason = 'VALUE_AVERAGE' AND order_date = ?", (current_time,))
            cursor.execute("DELETE FROM order_status_history WHERE order_uuid = ?", (test_uuid,))
            conn.commit()
            print("🧹 测试数据已清理")
        except:
            pass
        try:
            conn.close()
        except:
            pass

def test_margin_task_logic():
    """测试融资任务创建逻辑"""
    print("\n🧪 测试融资任务创建逻辑")
    
    try:
        # 模拟不同的现金情况（使用修正后的0.5%缓冲）
        test_cases = [
            {"available_cash": 10000, "shares": 1000, "price": 2.5, "expected": False},  # 现金充足
            {"available_cash": 1000, "shares": 1000, "price": 2.5, "expected": True},   # 现金不足
            {"available_cash": 2500, "shares": 1000, "price": 2.5, "expected": True},   # 刚好够但考虑缓冲
            {"available_cash": 2520, "shares": 1000, "price": 2.5, "expected": False},  # 略有余量
        ]

        for i, case in enumerate(test_cases):
            print(f"\n📋 测试用例 {i+1}：")
            print(f"   可用现金：{case['available_cash']}")
            print(f"   目标股数：{case['shares']}")
            print(f"   当前价格：{case['price']}")

            estimated_cost = case['shares'] * case['price'] * 1.005  # 加0.5%缓冲
            need_margin = estimated_cost > case['available_cash']

            print(f"   预估成本：{estimated_cost:.2f}")
            print(f"   需要融资：{need_margin}")
            print(f"   预期结果：{case['expected']}")

            if need_margin == case['expected']:
                print("   ✅ 逻辑正确")
            else:
                print("   ❌ 逻辑错误")
                return False
        
        print("\n✅ 融资任务创建逻辑测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 融资任务逻辑测试异常：{str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试回调函数修复效果\n")
    
    # 测试1：订单状态同步
    test1_result = test_order_status_sync()
    
    # 测试2：融资任务逻辑
    test2_result = test_margin_task_logic()
    
    # 总结
    print(f"\n📋 测试总结：")
    print(f"   订单状态同步：{'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   融资任务逻辑：{'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！修复效果良好。")
    else:
        print("\n⚠️  部分测试失败，需要进一步检查。")

if __name__ == "__main__":
    main()
