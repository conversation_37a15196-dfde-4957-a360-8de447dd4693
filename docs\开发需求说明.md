一个基于价值平均，并结合技术指标进行择时的量化投资策略。策略的核心思想是在市场沉睡期投资于高股息资产，而在激活期则切换至高成长性资产。

## 买入和卖出信号的产生

信号检测基于 **创业板ETF (159915)** 的价格数据，使用"EMA检测周期"的K线图进行计算。

技术指标计算公式（通达信格式）：
```
K1:=EXPMA(C,35);  // 35是EMA参数，可配置（2-500倍周期）
F1:=K1*0.85; // 0.85是底部相对比例，可配置
F2:=K1*1.90;  // 1.90是顶部相对比例，可配置
ENTERLONG:FILTER(CROSS(F1,C),8);  // 买入信号：收盘价下穿底部线，8个周期内不重复
EXITLONG:FILTER(CROSS(H,F2),10);  // 卖出信号：最高价上穿顶部线，10个周期内不重复
```

信号说明：
- **买入信号(ENTERLONG)**：当159915的收盘价向下跌破EMA底部线(F1)时触发，在EMA检测周期收盘后检测
- **卖出信号(EXITLONG)**：当159915的最高价向上突破EMA顶部线(F2)时触发，实盘实时监控
- **信号过滤**：买入信号8个检测周期内只能触发一次，卖出信号10个检测周期内只能触发一次
- **信号确认**：买入信号在检测周期K线收盘时确认，卖出信号实时确认5)** 的价格数据，使用"EMA检测周期"的K线图进行计算。

技术指标计算公式（通达信格式）：
```
K1:=EXPMA(C,35);  // 35是EMA参数，可配置（2-500倍周期）
F1:=K1*0.85; // 0.85是底部相对比例，可配置
F2:=K1*1.90;  // 1.90是顶部相对比例，可配置
ENTERLONG:FILTER(CROSS(F1,C),8);  // 买入信号：收盘价下穿底部线，8个周期内不重复
EXITLONG:FILTER(CROSS(H,F2),10);  // 卖出信号：最高价上穿顶部线，10个周期内不重复
```

信号说明：
- **买入信号(ENTERLONG)**：当159915的收盘价向下突破EMA底部线(F1)时触发
- **卖出信号(EXITLONG)**：当159915的最高价向上突破EMA顶部线(F2)时触发
- **信号过滤**：买入信号8个检测周期内只能触发一次，卖出信号10个检测周期内只能触发一次
- **信号确认**：在每个检测周期的K线收盘时确认信号略进行仓位管理。


# 一、 核心策略逻辑

本系统由两大核心部分构成，分别是仓位管理模块和择时模块。

## 仓位与资金管理

采用“价值平均策略” (Value Averaging) 来动态调整每期应投入的资金和持有的仓位数量。

## 买卖时点选择

利用技术指标来判断市场的“沉睡期”与“激活期”，从而决定策略的启动和清仓时点。
  

# 二、 策略执行流程

## 1. 沉睡期（未收到初始激活期买入信号）：
  - 操作： 系统默认处于沉睡期。在此期间，将所有可用资金买入并持有 **红利国企ETF (510720)**。
  - 目的： 获取稳定的分红收益，避免资金闲置。
    
## 2. 激活期（收到初始激活期买入信号）：
  - 触发： 当预设的技术指标发出买入信号后，系统进入激活期。
  - 操作：
    - 首先，根据价值平均策略计算出的当期应投资金额，卖出等额的红利国企ETF (510720) 以获取现金。
    - 然后，将这笔现金用于买入 **创业板ETF (159915)**。
    - 此后的每个投资周期，均按照价值平均策略的要求进行仓位调整。
      

# 三、 策略参数设置

为了有效监控和执行该策略，系统应具备如下参数设置（所有可设置的参数，都放在策略python文件的开始位置，方便修改！）：

- 沉睡期基金仓位：默认100%，即所有资金持有红利国企ETF (510720)。
- 每期投入金额： 设定每个投资周期期望增加的资产价值。
- 投资周期：支持60分钟、日、周、季、月等不同时间频率的投资操作，默认使用月线。
- EMA检测周期：默认使用季线，支持60分钟线、日线、周线、季线、月线等。
- EMA参数：默认使用35X(X代表日数、周数、季度数等，取决于上面的投资周期)指数移动平均线 (EMA)，允许用户在2X至500X之间进行调整。
- 底部相对比例：默认0.85。
- 顶部相对比例：默认1.90。
- 激活期基金代码：159915（易方达创业板etf）。
- 沉睡期基金代码：510720（国泰上证国有企业红利etf）。


# 四、 策略算法说明

## 如果当前在沉睡期，当出现卖出信号时，将“沉睡期基金仓位”的资金买入红利国企ETF (510720)。

## 如果当前在激活期，当出现卖出信号时，将当前持有的创业板ETF (159915) 卖出，并将资金转入沉睡期基金仓位，即红利国企ETF (510720)，由此继续进入沉睡期。

## 只要没有出现过初始激活期买入信号，系统将一直处于沉睡期，所有资金持有红利国企ETF (510720)。

## 如果出现初始激活期买入信号，系统将进入激活期，并根据价值平均策略计算出当期应投资金额，优先从持有的红利国企ETF (510720) 卖出，如果金额不足，则使用账户资金，再不够就使用融资资金补全；只要当前是激活期，所有后续的买入信号，全部忽略。

### 当期应投资金额的计算方法说明：

以“投资周期”参数指定的周期线，往前回溯5年内同一周期线的历史收盘价最高点，以该最高点所在K线作为第1期，一直推算到当前K线，以“每期投入金额”累加作为标的，进行价值跟踪，从而决定应该买入或卖出的“激活期基金代码”份额。具体案例如下：

例如现在有3期，每期投入金额为10000元，那么第1期追踪的标的就是10000元，假设收盘价为19元，应买入526股，当前的持仓价值为9994元；第2期追踪的标的则为20000元，假设收盘价为30元，那么之前买入的持仓价值将变成了15780元，此时需要买入20000-15780=4219元的份额，那么需要继续买入140股，当前持仓666股；第3期追踪的标的则为30000元，假设收盘价为80元，那么之前买入的持仓价值将变成了(526+140)*80 = 53280元，此时需要卖出53280 - 30000 = 23280元的份额，那么应卖出23280/80 = 291股，当前持仓375股。

**跳过周期处理说明**：
如果某期因资金不足无法执行投资，系统将：
1. 记录跳过周期的详细日志（期数、目标金额、失败原因、时间）
2. 下一期计算时，目标金额仍按完整公式累加，包含跳过期数的应投金额
3. 例如：第2期因资金不足跳过，第3期目标金额仍为30000元（3×10000），而非20000元

### 资金管理细则：

**买入资金调用顺序**：510720（沉睡期基金）-> 账户资金 -> 融资
- 融资资金额度：根据账户的额度来操作
- 买入股数规则：必须是100股的正整数倍，不足100股的金额部分不执行买入

**卖出资金处理**：自动还融资资金 -> 剩余资金买入510720
- 卖出股数规则：最少100股，可以不是100的倍数

**交易执行时机**：
- **买入信号检测**：在"EMA检测周期"最后一日收盘后检测，如处于沉睡期且出现信号，下一个交易日执行买入
- **卖出信号检测**：实盘实时检测，一旦出现立即执行清仓转510720
- **定投调整检测**：每月月末最后一个交易日收盘后检测价值平均策略，下一个交易日执行标的跟踪调整
- 异常处理：ETF停牌或系统故障时，将交易指令存储到SQLite数据库，待下一个可交易日执行

**交易失败处理机制**：
- **重试策略**：交易失败后自动重试3次，每次间隔5分钟
- **降级处理**：
  - 买入失败：记录失败原因，下一个交易周期重新尝试
  - 卖出失败：立即报警并记录，持续重试直到成功（因为卖出信号紧急）
  - 多次卖出失败：连续失败超过3次立即发送紧急告警
- **失败分类处理**：
  - 网络/系统故障：自动重试
  - 资金不足：直接报错停止执行，不允许调整金额（保护策略完整性）
    - 记录跳过周期日志到数据库
    - 下次计算时仍需累加跳过期数的目标金额，保持价值平均策略连续性
  - ETF停牌：延迟到复牌后执行
  - 价格偏离过大：使用市价单重试
- **监控报警**：所有失败情况记录到数据库，关键失败发送报警通知

## 买入和卖出信号的产生

默认是用“EMA检测周期”K线，“EMA参数”、“底部相对比例”、“顶部相对比例”，根据下面的通达信公式来判断。

```
K1:=EXPMA(C,35);  // 35是EMA参数
F1:=K1*0.85; // 0.85是底部相对比例
F2:=K1*1.90;  // 1.90是顶部相对比例
ENTERLONG:FILTER(CROSS(F1,C),8);
EXITLONG:FILTER(CROSS(H,F2),10);
```

# 五、 系统技术要求

## 程序文件结构
只允许单个python文件，一切从简！根据我提供的技术文档的要求，编写必要的方法

## 可读性要求
源代码中，每个方法一定要附带详细的注释，在方法中也要注意重要位置务必注释，所有注释均采用简体中文！

## 交易接口
- 严格使用技术文档提供的交易接口进行实盘交易

## 数据存储
- 使用SQLite数据库存储（要告诉我具体要安装哪个版本的sqlite操作库）：
  - 策略运行状态（当前阶段、最后检测时间、信号历史等）
  - 待执行交易指令（支持延迟执行、重试机制）
  - 持仓记录和价值平均计算历史
  - 交易执行日志和失败记录（包括失败原因、重试次数、最终状态）
  - 跳过周期记录（期数、目标金额、跳过原因、时间戳）
  - 为系统恢复和故障处理提供数据支持

## 运行机制
- **买入信号监控（沉睡期->激活期）**：在EMA检测周期收盘后运行检测
- **卖出信号监控（激活期->沉睡期）**：实盘实时监控，触发后立即执行
- **定投周期调整**：每月月末最后一个交易日收盘后检测价值平均策略

# 六、 回测与分析
采用策略运行的软件中自带的回测分析功能即可