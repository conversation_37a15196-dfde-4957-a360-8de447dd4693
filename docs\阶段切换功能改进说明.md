# 阶段切换功能改进说明

## 改进背景

用户发现 `execute_phase_transition` 方法存在以下问题：
1. 从 sleeping -> active 状态切换时，没有记录开始时间（start_period_date）和开始价格（start_period_price）
2. 在 trade_orders 表中没有看到交易记录
3. 缺少详细的日志记录，特别是股票份额检查、股票购买等操作

## 改进内容

### 1. 价值平均起始期设置

#### 问题修复
在从 sleeping -> active 切换时，现在会正确设置价值平均起始期信息：

```python
# 2. 设置价值平均起始期信息
log_message("INFO", "阶段切换", "步骤2：设置价值平均起始期信息")
try:
    start_date, start_price = get_historical_highest_price(ACTIVE_FUND_CODE, 5, ContextInfo)
    g_strategy_status['start_period_date'] = start_date
    g_strategy_status['start_period_price'] = start_price
    log_message("INFO", "阶段切换", f"价值平均起始期设置：日期={start_date}, 价格={start_price:.4f}")
except Exception as e:
    log_message("ERROR", "阶段切换", f"设置价值平均起始期失败：{str(e)}")
    # 使用默认值
    g_strategy_status['start_period_date'] = current_time[:10]  # YYYY-MM-DD
    g_strategy_status['start_period_price'] = get_current_price(ACTIVE_FUND_CODE, ContextInfo)
    log_message("WARNING", "阶段切换", f"使用默认起始期：日期={g_strategy_status['start_period_date']}, 价格={g_strategy_status['start_period_price']:.4f}")
```

#### 关键改进
- **正确设置起始期**：调用 `get_historical_highest_price()` 获取5日内最高价作为起始期
- **异常处理**：如果获取失败，使用当前日期和价格作为默认值
- **详细日志**：记录设置过程和结果

### 2. 详细日志记录

#### 股票份额检查日志
```python
# 检查持仓
log_message("INFO", "阶段切换", "步骤1：检查510720持仓")
position_510720 = get_current_position(SLEEPING_FUND_CODE)
log_message("INFO", "阶段切换", f"510720当前持仓：{position_510720['shares']}股，市值：{position_510720['market_value']:.2f}元")

if position_510720['shares'] > 0:
    log_message("INFO", "阶段切换", f"准备卖出510720：{position_510720['shares']}股")
    # ... 执行卖出
    log_message("INFO", "阶段切换", f"成功卖出510720：{position_510720['shares']}股")
else:
    log_message("INFO", "阶段切换", "510720无持仓，跳过卖出步骤")
```

#### 股票购买日志
```python
# 买入股票
log_message("INFO", "阶段切换", "步骤3：买入159915")
account_info = get_account_info(ContextInfo)
available_cash = account_info['available_cash']
current_price = get_current_price(ACTIVE_FUND_CODE, ContextInfo)

log_message("INFO", "阶段切换", f"账户可用资金：{available_cash:.2f}元")
log_message("INFO", "阶段切换", f"159915当前价格：{current_price:.4f}元")

shares_to_buy = int(available_cash / current_price / MIN_TRADE_SHARES) * MIN_TRADE_SHARES
log_message("INFO", "阶段切换", f"计算可买入股数：{shares_to_buy}股")

if shares_to_buy >= MIN_TRADE_SHARES:
    log_message("INFO", "阶段切换", f"准备买入159915：{shares_to_buy}股")
    success = execute_trade_order(...)
    if success:
        log_message("INFO", "阶段切换", f"成功买入159915：{shares_to_buy}股")
    else:
        log_message("ERROR", "阶段切换", f"买入159915失败：{shares_to_buy}股")
else:
    log_message("WARNING", "阶段切换", f"可买入股数不足最小交易单位：{shares_to_buy} < {MIN_TRADE_SHARES}")
```

#### 状态更新日志
```python
# 更新策略状态
log_message("INFO", "阶段切换", "步骤4：更新策略状态")
g_strategy_status['current_phase'] = 'active'
if g_strategy_status['first_activation_time'] is None:
    g_strategy_status['first_activation_time'] = current_time
    log_message("INFO", "阶段切换", f"设置首次激活时间：{current_time}")
else:
    log_message("INFO", "阶段切换", f"策略已激活过，首次激活时间：{g_strategy_status['first_activation_time']}")
```

### 3. 交易记录确认

#### 交易流程
`execute_phase_transition` -> `execute_trade_order` -> `record_trade_order` -> 数据库记录

#### 记录内容
每次交易都会在 `trade_orders` 表中记录：
- 订单日期、股票代码、订单类型
- 目标股数、实际股数、实际价格
- 订单状态、执行时间、错误信息

#### 状态跟踪
- **PENDING**：订单已创建，等待执行
- **SUCCESS**：订单执行成功
- **FAILED**：订单执行失败
- **SIMULATED**：回测模式模拟执行

### 4. 错误处理改进

#### 关键错误处理
```python
if not success:
    log_message("ERROR", "阶段切换", "卖出510720失败，阶段切换中止")
    return  # 中止切换流程
```

#### 警告处理
```python
if shares_to_buy < MIN_TRADE_SHARES:
    log_message("WARNING", "阶段切换", f"可买入股数不足最小交易单位：{shares_to_buy} < {MIN_TRADE_SHARES}")
```

#### 异常捕获
```python
except Exception as e:
    error_msg = f"执行阶段切换失败：{str(e)}"
    log_message("ERROR", "阶段切换", error_msg)
    print(f"阶段切换异常详情：{traceback.format_exc()}")
```

## 日志输出示例

### 成功的阶段切换日志
```
[2025-08-07 12:49:34] INFO - 阶段切换: === 开始从沉睡期切换到激活期 ===
[2025-08-07 12:49:34] INFO - 阶段切换: 步骤1：检查510720持仓
[2025-08-07 12:49:34] INFO - 阶段切换: 510720当前持仓：1000股，市值：2500.00元
[2025-08-07 12:49:34] INFO - 阶段切换: 准备卖出510720：1000股
[2025-08-07 12:49:34] INFO - 阶段切换: 成功卖出510720：1000股
[2025-08-07 12:49:34] INFO - 阶段切换: 步骤2：设置价值平均起始期信息
[2025-08-07 12:49:34] INFO - 阶段切换: 价值平均起始期设置：日期=2025-08-07, 价格=1.2345
[2025-08-07 12:49:34] INFO - 阶段切换: 步骤3：买入159915
[2025-08-07 12:49:34] INFO - 阶段切换: 账户可用资金：2500.00元
[2025-08-07 12:49:34] INFO - 阶段切换: 159915当前价格：1.2345元
[2025-08-07 12:49:34] INFO - 阶段切换: 计算可买入股数：2000股
[2025-08-07 12:49:34] INFO - 阶段切换: 准备买入159915：2000股
[2025-08-07 12:49:34] INFO - 阶段切换: 成功买入159915：2000股
[2025-08-07 12:49:34] INFO - 阶段切换: 步骤4：更新策略状态
[2025-08-07 12:49:34] INFO - 阶段切换: 设置首次激活时间：2025-08-07 12:50:00
[2025-08-07 12:49:34] INFO - 阶段切换: === 阶段切换完成：sleeping -> active ===
```

### 异常情况日志
```
[2025-08-07 12:49:34] WARNING - 阶段切换: 510720无持仓，跳过卖出步骤
[2025-08-07 12:49:34] WARNING - 阶段切换: 可买入股数不足最小交易单位：50 < 100
[2025-08-07 12:49:34] ERROR - 阶段切换: 卖出510720失败，阶段切换中止
[2025-08-07 12:49:34] ERROR - 阶段切换: 设置价值平均起始期失败：网络连接超时
[2025-08-07 12:49:34] WARNING - 阶段切换: 使用默认起始期：日期=2025-08-07, 价格=1.2345
```

## 数据库记录验证

### 策略状态表 (strategy_status)
切换完成后会包含：
- `current_phase`: 'active'
- `first_activation_time`: 首次激活时间
- `start_period_date`: 价值平均起始期日期
- `start_period_price`: 价值平均起始期价格

### 交易订单表 (trade_orders)
每次交易都会记录：
- 卖出510720的订单记录
- 买入159915的订单记录
- 包含完整的执行状态和结果

## 测试验证

创建了完整的测试用例：
1. ✅ 策略状态更新测试
2. ✅ 交易订单记录测试
3. ✅ 详细日志输出测试
4. ✅ 错误场景处理测试

## 改进效果

1. **完整的状态管理**：正确设置和保存价值平均起始期信息
2. **详细的操作日志**：每个步骤都有清晰的日志记录
3. **完善的错误处理**：异常情况有相应的日志和处理逻辑
4. **可追溯的交易记录**：所有交易都会记录到数据库
5. **调试友好**：通过日志可以清楚了解每个步骤的执行情况

## 使用建议

1. **监控日志**：关注阶段切换的日志输出，确保每个步骤正常执行
2. **检查数据库**：定期检查 `strategy_status` 和 `trade_orders` 表的记录
3. **异常处理**：如果看到ERROR级别的日志，需要及时处理
4. **状态验证**：确保 `start_period_date` 和 `start_period_price` 在激活期正确设置
