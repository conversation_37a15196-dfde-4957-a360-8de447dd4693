#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 get_current_time 函数修复
验证在实盘模式下 g_current_bar_time 是否正确设置
"""

import datetime
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 模拟 iQuant 环境
class MockContextInfo:
    def __init__(self, is_backtest=False):
        self.is_backtest = is_backtest
        self.barpos = 100
    
    def get_bar_timetag(self, barpos):
        # 模拟获取K线时间戳
        if self.is_backtest:
            # 返回一个模拟的时间戳（毫秒）
            return int(datetime.datetime(2024, 8, 15, 9, 30, 0).timestamp() * 1000)
        else:
            return None

def test_get_current_time():
    """
    测试 get_current_time 函数
    """
    print("=" * 50)
    print("测试 get_current_time 函数修复")
    print("=" * 50)
    
    try:
        # 导入修复后的函数
        from value_averaging_strategy import get_current_time, g_current_bar_time, IS_BACKTEST_MODE
        
        print(f"当前 IS_BACKTEST_MODE 设置: {IS_BACKTEST_MODE}")
        
        # 测试实盘模式
        print("\n1. 测试实盘模式:")
        mock_context_live = MockContextInfo(is_backtest=False)
        
        # 在调用前检查 g_current_bar_time
        print(f"调用前 g_current_bar_time: {g_current_bar_time}")
        
        # 调用 get_current_time
        current_time = get_current_time(mock_context_live)
        print(f"get_current_time 返回: {current_time}")
        
        # 检查 g_current_bar_time 是否被设置
        print(f"调用后 g_current_bar_time: {g_current_bar_time}")
        
        if g_current_bar_time is not None:
            print("✅ 实盘模式下 g_current_bar_time 已正确设置")
            
            # 测试在 update_technical_indicators 中使用 g_current_bar_time
            try:
                time_str = g_current_bar_time.strftime('%Y%m%d')
                print(f"✅ g_current_bar_time.strftime('%Y%m%d') = {time_str}")
            except Exception as e:
                print(f"❌ 使用 g_current_bar_time.strftime 失败: {str(e)}")
        else:
            print("❌ 实盘模式下 g_current_bar_time 未设置")
        
        # 测试回测模式（如果可能）
        print("\n2. 测试回测模式:")
        mock_context_backtest = MockContextInfo(is_backtest=True)
        
        # 临时修改 IS_BACKTEST_MODE 来测试回测逻辑
        import value_averaging_strategy
        original_backtest_mode = value_averaging_strategy.IS_BACKTEST_MODE
        value_averaging_strategy.IS_BACKTEST_MODE = True
        
        try:
            current_time_backtest = get_current_time(mock_context_backtest)
            print(f"回测模式 get_current_time 返回: {current_time_backtest}")
            print(f"回测模式 g_current_bar_time: {g_current_bar_time}")
            
            if g_current_bar_time is not None:
                print("✅ 回测模式下 g_current_bar_time 已正确设置")
                time_str = g_current_bar_time.strftime('%Y%m%d')
                print(f"✅ 回测模式 g_current_bar_time.strftime('%Y%m%d') = {time_str}")
            else:
                print("❌ 回测模式下 g_current_bar_time 未设置")
        finally:
            # 恢复原始设置
            value_averaging_strategy.IS_BACKTEST_MODE = original_backtest_mode
        
        print("\n3. 测试异常情况:")
        # 创建一个会抛出异常的 mock 对象
        class ErrorContextInfo:
            def __init__(self):
                pass
            
            def get_bar_timetag(self, barpos):
                raise Exception("模拟异常")
        
        error_context = ErrorContextInfo()
        
        try:
            current_time_error = get_current_time(error_context)
            print(f"异常情况 get_current_time 返回: {current_time_error}")
            print(f"异常情况 g_current_bar_time: {g_current_bar_time}")
            
            if g_current_bar_time is not None:
                print("✅ 异常情况下 g_current_bar_time 已正确设置")
                time_str = g_current_bar_time.strftime('%Y%m%d')
                print(f"✅ 异常情况 g_current_bar_time.strftime('%Y%m%d') = {time_str}")
            else:
                print("❌ 异常情况下 g_current_bar_time 未设置")
        except Exception as e:
            print(f"测试异常情况时出错: {str(e)}")
        
        print("\n🎉 测试完成！")
        
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        print("请确保 value_averaging_strategy.py 文件存在且可导入")
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """
    主函数
    """
    test_get_current_time()

if __name__ == "__main__":
    main()
