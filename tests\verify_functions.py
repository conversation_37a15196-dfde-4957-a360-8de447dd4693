# -*- coding: utf-8 -*-
"""
验证激进策略函数修改是否正确
"""

import re

def check_function_calls():
    """检查函数调用是否正确"""
    print("=" * 50)
    print("检查激进策略函数调用")
    print("=" * 50)
    
    try:
        with open('aggressive_strategy.py', 'r', encoding='gbk', errors='ignore') as f:
            content = f.read()
        
        # 检查是否还有不存在的函数调用
        problematic_calls = [
            'execute_159915_trade',
            'execute_510720_trade',
            'execute_sleeping_to_active_transition_async'
        ]
        
        correct_calls = [
            'execute_active_period_trade_async',
            'execute_active_to_sleeping_transition_async',
            'get_current_position',
            'calculate_position_from_trades'
        ]
        
        print("检查问题函数调用：")
        for call in problematic_calls:
            count = len(re.findall(call, content))
            if count > 0:
                print(f"  ❌ {call}: 发现 {count} 次调用")
            else:
                print(f"  ✅ {call}: 已清理")
        
        print("\n检查正确函数调用：")
        for call in correct_calls:
            count = len(re.findall(call, content))
            if count > 0:
                print(f"  ✅ {call}: 发现 {count} 次调用")
            else:
                print(f"  ⚠️ {call}: 未发现调用")
        
        # 检查激进策略核心函数
        aggressive_functions = [
            'execute_aggressive_buy_active',
            'execute_aggressive_buy_sleeping', 
            'execute_aggressive_sell_sleeping_buy_active',
            'execute_aggressive_sell_active_buy_sleeping',
            'get_latest_signal_from_db'
        ]
        
        print("\n检查激进策略核心函数：")
        for func in aggressive_functions:
            if f"def {func}" in content:
                print(f"  ✅ {func}: 已定义")
            else:
                print(f"  ❌ {func}: 未定义")
        
        print("\n" + "=" * 50)
        print("函数调用检查完成")
        print("=" * 50)
        
    except Exception as e:
        print(f"检查失败：{str(e)}")

if __name__ == "__main__":
    check_function_calls()
