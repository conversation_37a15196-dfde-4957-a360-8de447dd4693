# -*- coding: utf-8 -*-
"""
强制激活功能测试脚本
用于验证强制激活期功能是否正常工作
"""

import os
import sys
import sqlite3
import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_force_active_functionality():
    """测试强制激活功能"""
    print("=" * 60)
    print("强制激活功能测试")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from value_averaging_strategy import (
            init_database, load_strategy_status, g_strategy_status, g_db_connection,
            force_activate_strategy, manual_set_active_status, insert_simulated_buy_signal,
            reset_strategy_state, FORCE_ACTIVE_MODE, FORCE_ACTIVE_START_DATE, FORCE_ACTIVE_START_PRICE
        )
        
        print("✓ 模块导入成功")
        
        # 1. 初始化数据库
        print("\n1. 初始化数据库...")
        init_database()
        load_strategy_status()
        print(f"✓ 数据库初始化完成，当前状态：{g_strategy_status['current_phase']}")
        
        # 2. 测试重置功能
        print("\n2. 测试重置功能...")
        reset_strategy_state()
        print(f"✓ 重置完成，当前状态：{g_strategy_status['current_phase']}")
        
        # 3. 测试模拟信号插入
        print("\n3. 测试模拟信号插入...")
        success = insert_simulated_buy_signal()
        if success:
            print("✓ 模拟买入信号插入成功")
            
            # 查询插入的信号
            cursor = g_db_connection.cursor()
            cursor.execute("""
                SELECT signal_date, signal_type, signal_price, is_valid 
                FROM signal_history 
                WHERE signal_type = 'ENTERLONG' AND DATE(signal_date) = ?
                ORDER BY signal_date DESC LIMIT 1
            """, (FORCE_ACTIVE_START_DATE,))
            
            signal = cursor.fetchone()
            if signal:
                print(f"  信号详情：{signal[0]} - {signal[1]} - 价格：{signal[2]:.4f}")
            else:
                print("✗ 未找到插入的信号记录")
        else:
            print("✗ 模拟买入信号插入失败")
        
        # 4. 测试手动设置激活状态
        print("\n4. 测试手动设置激活状态...")
        success = manual_set_active_status(FORCE_ACTIVE_START_DATE, FORCE_ACTIVE_START_PRICE)
        if success:
            print("✓ 手动设置激活状态成功")
            print(f"  当前阶段：{g_strategy_status['current_phase']}")
            print(f"  起始日期：{g_strategy_status['start_period_date']}")
            print(f"  起始价格：{g_strategy_status['start_period_price']}")
        else:
            print("✗ 手动设置激活状态失败")
        
        # 5. 测试强制激活功能
        print("\n5. 测试强制激活功能...")
        # 先重置到沉睡期
        reset_strategy_state()
        print(f"  重置后状态：{g_strategy_status['current_phase']}")
        
        # 执行强制激活
        success = force_activate_strategy()
        if success:
            print("✓ 强制激活成功")
            print(f"  当前阶段：{g_strategy_status['current_phase']}")
            print(f"  首次激活时间：{g_strategy_status['first_activation_time']}")
        else:
            print("✗ 强制激活失败")
        
        # 6. 测试重复激活保护
        print("\n6. 测试重复激活保护...")
        success = force_activate_strategy()
        if success:
            print("✓ 重复激活保护正常（已激活状态下调用返回True）")
        else:
            print("✗ 重复激活保护异常")
        
        # 7. 查询最终状态
        print("\n7. 查询最终状态...")
        load_strategy_status()  # 重新加载确保数据一致性
        print(f"  当前阶段：{g_strategy_status['current_phase']}")
        print(f"  最后检测时间：{g_strategy_status['last_check_time']}")
        if g_strategy_status['current_phase'] == 'active':
            print(f"  首次激活时间：{g_strategy_status['first_activation_time']}")
            print(f"  起始期日期：{g_strategy_status['start_period_date']}")
            print(f"  起始期价格：{g_strategy_status['start_period_price']}")
            print(f"  当前期数：{g_strategy_status['current_period']}")
        
        # 8. 查询信号历史
        print("\n8. 查询信号历史...")
        cursor = g_db_connection.cursor()
        cursor.execute("""
            SELECT signal_date, signal_type, signal_price, is_valid 
            FROM signal_history 
            WHERE signal_type = 'ENTERLONG'
            ORDER BY signal_date DESC 
            LIMIT 3
        """)
        
        signals = cursor.fetchall()
        if signals:
            print("  最近的买入信号记录：")
            for signal in signals:
                status = "有效" if signal[3] else "无效"
                print(f"    {signal[0]} - {signal[1]} - 价格：{signal[2]:.4f} - {status}")
        else:
            print("  未找到买入信号记录")
        
        print("\n" + "=" * 60)
        print("测试完成！")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n✗ 测试失败：{str(e)}")
        import traceback
        print("详细错误信息：")
        print(traceback.format_exc())
        return False


def test_configuration_display():
    """测试配置显示"""
    print("\n" + "=" * 60)
    print("当前配置信息")
    print("=" * 60)
    
    try:
        from value_averaging_strategy import (
            FORCE_ACTIVE_MODE, FORCE_ACTIVE_START_DATE, FORCE_ACTIVE_START_PRICE,
            PERIOD_INVESTMENT_AMOUNT, INVESTMENT_CYCLE, EMA_DETECTION_CYCLE
        )
        
        print("强制激活配置：")
        print(f"  FORCE_ACTIVE_MODE = {FORCE_ACTIVE_MODE}")
        print(f"  FORCE_ACTIVE_START_DATE = '{FORCE_ACTIVE_START_DATE}'")
        print(f"  FORCE_ACTIVE_START_PRICE = {FORCE_ACTIVE_START_PRICE}")
        
        print("\n策略配置：")
        print(f"  每期投入金额 = {PERIOD_INVESTMENT_AMOUNT:,}元")
        print(f"  投资周期 = {INVESTMENT_CYCLE}")
        print(f"  EMA检测周期 = {EMA_DETECTION_CYCLE}")
        
        if FORCE_ACTIVE_MODE:
            print("\n⚠️  当前强制激活模式已开启")
            print("   策略启动时会自动进入激活期")
        else:
            print("\n✓ 当前为正常信号检测模式")
            print("   策略将等待买入信号触发激活期")
            
    except Exception as e:
        print(f"配置信息获取失败：{str(e)}")


def main():
    """主函数"""
    print("强制激活功能测试脚本")
    print("用于验证强制激活期功能是否正常工作")
    
    # 显示当前配置
    test_configuration_display()
    
    # 询问是否继续测试
    print("\n" + "=" * 60)
    response = input("是否继续执行功能测试？(y/n): ").strip().lower()
    
    if response in ['y', 'yes', '是', '1']:
        success = test_force_active_functionality()
        
        if success:
            print("\n🎉 所有测试通过！强制激活功能正常工作。")
            print("\n客户部署建议：")
            print("1. 设置 FORCE_ACTIVE_MODE = True")
            print("2. 确认 FORCE_ACTIVE_START_DATE 和 FORCE_ACTIVE_START_PRICE")
            print("3. 启动策略，系统会自动进入激活期")
        else:
            print("\n❌ 测试失败，请检查错误信息并修复问题。")
    else:
        print("测试已取消。")


if __name__ == "__main__":
    main()
