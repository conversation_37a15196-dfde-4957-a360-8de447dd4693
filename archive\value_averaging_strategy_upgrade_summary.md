# 价值平均策略交易逻辑升级总结

## 升级概述

基于 `simple_trading_test.py` 中成熟的交易逻辑，对 `value_averaging_strategy.py` 进行了全面升级，解决了订单匹配、费用计算、状态管理等关键问题。

## 主要升级内容

### 1. 🔧 **UUID匹配机制**

#### 问题解决
- ❌ **修改前**：使用 `get_last_order_id` 获取订单号，存在时差问题
- ✅ **修改后**：使用UUID作为 `passorder` 第10个参数，通过 `m_strRemark` 精确匹配

#### 具体修改
```python
# 修改前
result = passorder(34, 1101, ContextInfo.accID, stock_code, 44, -1, shares, 
                  '价值平均策略', 1, f'任务队列卖出{shares}股', ContextInfo)
order_id = get_last_order_id(ContextInfo.accID, 'stock', 'order')

# 修改后
result = passorder(34, 1101, ContextInfo.accID, stock_code, 44, -1, shares, 
                  '价值平均策略', 1, order_uuid, ContextInfo)  # 使用UUID作为备注
return f"TEMP_SELL_{order_uuid[:8]}"  # 返回临时ID
```

### 2. 🗄️ **数据库结构升级**

#### 新增字段
```sql
-- trade_task_queue 表添加
ALTER TABLE trade_task_queue ADD COLUMN order_uuid TEXT;

-- 新增交易执行记录表
CREATE TABLE trade_execution_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    trade_time TEXT NOT NULL,
    trade_type TEXT NOT NULL,
    stock_code TEXT NOT NULL,
    shares INTEGER NOT NULL,
    price REAL,
    amount REAL,
    fees REAL,
    order_id TEXT,
    order_uuid TEXT,  -- 用于回调匹配
    status TEXT NOT NULL,
    error_message TEXT,
    created_time TEXT NOT NULL
);

-- 新增费用明细表
CREATE TABLE trade_fee_details (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    execution_log_id INTEGER,
    order_uuid TEXT NOT NULL,
    commission REAL DEFAULT 0,
    stamp_tax REAL DEFAULT 0,
    transfer_fee REAL DEFAULT 0,
    other_fees REAL DEFAULT 0,
    total_fees REAL DEFAULT 0,
    net_amount REAL DEFAULT 0,
    created_time TEXT NOT NULL
);

-- 新增订单状态历史表
CREATE TABLE order_status_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_uuid TEXT NOT NULL,
    order_id TEXT,
    stock_code TEXT,
    order_status INTEGER NOT NULL,
    status_desc TEXT,
    volume_traded INTEGER DEFAULT 0,
    volume_total INTEGER DEFAULT 0,
    callback_time TEXT NOT NULL,
    created_time TEXT NOT NULL
);
```

### 3. 💰 **费用计算系统**

#### 新增费用率常量
```python
COMMISSION_FEE_RATE = 0.0003   # 佣金费率（万分之3）
COMMISSION_FEE_MIN = 5         # 最低交易佣金（元）
SELL_TAX_RATE = 0.001          # 印花税率（千分之1，仅卖出）
TRANSFER_FEE_RATE = 0.00002    # 过户费率（万分之0.2，仅上海）
```

#### 统一费用计算函数
```python
def calculate_trading_fees(amount: float, shares: int, trade_type: str, stock_code: str = None) -> dict:
    # 佣金：max(成交金额 × 0.0003, 5.0)
    # 印花税：成交金额 × 0.001（仅卖出）
    # 过户费：max(成交金额 × 0.00002, 1.0)（仅上海股票）
    return {
        'commission': commission,
        'stamp_tax': stamp_tax,
        'transfer_fee': transfer_fee,
        'total_fees': total_fees,
        'net_amount': net_amount,
        'gross_amount': amount
    }
```

### 4. 📋 **完整的订单状态处理**

#### 状态映射
```python
status_map = {
    0: "等待结束", 48: "未报", 49: "待报", 50: "已报",
    51: "已报待撤", 52: "部成待撤", 53: "部撤", 54: "已撤",
    55: "部成", 56: "已成", 57: "废单", 86: "已确认", 255: "未知"
}
```

#### 最终状态处理
```python
final_statuses = {
    54: ("CANCELLED", "已撤销"),    # 已撤
    56: ("COMPLETED", "已成交"),    # 已成
    57: ("FAILED", "废单")          # 废单
}
```

### 5. 🔄 **回调函数全面升级**

#### 订单回调升级
```python
def handle_order_callback(self, orderInfo):
    # 1. 调试信息：打印所有属性
    # 2. 获取UUID：从 m_strRemark 字段
    # 3. 多属性名尝试：获取真实订单ID
    # 4. 状态历史记录：记录所有状态变化
    # 5. 最终状态处理：更新任务状态
```

#### 成交回调升级
```python
def handle_deal_callback(self, dealInfo):
    # 1. 多属性名尝试：获取成交数据
    # 2. 费用计算：印花税、过户费、佣金
    # 3. 详细记录：执行记录 + 费用明细
    # 4. 净收益计算：考虑所有费用
```

### 6. 🛠️ **任务创建和执行升级**

#### 任务创建
```python
def create_task(...) -> tuple:
    order_uuid = str(uuid.uuid4())  # 生成UUID
    # 存储到数据库
    return task_id, order_uuid  # 返回任务ID和UUID
```

#### 任务执行
```python
def execute_sell_task(self, task: Dict, ContextInfo):
    order_uuid = task['order_uuid']  # 获取UUID
    order_id = self.place_sell_order(stock_code, actual_shares, order_uuid, ContextInfo)
```

## 升级效果对比

### 修改前的问题
```
❌ 订单匹配错乱：get_last_order_id 时差问题
❌ 费用计算不准确：缺少详细费用跟踪
❌ 状态处理不完整：只处理部分订单状态
❌ 调试信息不足：难以排查问题
❌ 数据记录不完整：缺少历史和明细
```

### 修改后的效果
```
✅ 精确订单匹配：UUID确保一一对应
✅ 完整费用跟踪：佣金、印花税、过户费
✅ 全状态处理：54已撤、56已成、57废单等
✅ 详细调试信息：属性输出、计算过程
✅ 完整数据记录：状态历史、费用明细
```

## 预期运行效果

### 订单回调
```
🔍 订单对象所有属性：
   m_strOrderSysID = 807146084
   m_strRemark = abc-def-123-456
   m_nOrderStatus = 56
   ...
✓ 找到订单ID属性：m_strOrderSysID = 807146084
收到订单回调：UUID=abc-def-123-456，订单ID=807146084，状态=56(已成)，股票=159915.SZ，成交量=100/100
✓ 订单状态历史已记录：UUID=abc-def-123-456，状态=已成
✓ 任务状态已更新：UUID=abc-def-123-456，任务ID=1，状态=COMPLETED(已成交)
```

### 成交回调
```
🔍 成交对象所有属性：
   m_dPrice = 2.4500
   m_dCommission = 5.00
   m_strRemark = abc-def-123-456
   ...
✓ 找到成交价属性：m_dPrice = 2.4500
✓ 找到手续费属性：m_dCommission = 5.00
✓ 计算印花税：245.00 × 0.1% = 0.25元（卖出）

收到成交回调：UUID=abc-def-123-456，订单ID=807146084，股票=159915.SZ
  成交详情：100股 × 2.4500元 = 245.00元
  费用明细：佣金5.00元
           印花税0.25元（卖出税率0.1%）
  总费用：5.25元，净额：239.75元，时间：14:30:25

✓ 交易执行记录已保存：UUID=abc-def-123-456，订单ID=807146084，记录ID=1
✓ 费用明细已记录：佣金5.00，印花税0.25，过户费0.00
```

## 技术优势

### 1. 可靠性提升
- ✅ UUID精确匹配，杜绝订单错乱
- ✅ 完整状态处理，避免状态残留
- ✅ 多重容错机制，提高稳定性

### 2. 数据完整性
- ✅ 详细的费用跟踪和计算
- ✅ 完整的状态历史记录
- ✅ 精确的净收益计算

### 3. 调试友好
- ✅ 详细的属性调试输出
- ✅ 完整的计算过程日志
- ✅ 清晰的状态变化跟踪

### 4. 扩展性强
- ✅ 模块化的费用计算系统
- ✅ 灵活的状态处理机制
- ✅ 完善的数据库设计

这次升级使 `value_averaging_strategy.py` 具备了与 `simple_trading_test.py` 同等的交易可靠性和数据完整性，为价值平均策略提供了坚实的技术基础。
