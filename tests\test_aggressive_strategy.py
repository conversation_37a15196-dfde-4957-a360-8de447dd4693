# encoding:gbk
"""
测试激进策略的基本功能
"""

import sys
import os
import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入激进策略模块
from aggressive_strategy import (
    init_database, load_strategy_status, get_latest_signal_from_db,
    execute_trading_logic, TOTAL_INVESTMENT_AMOUNT, ACTIVE_FUND_CODE, SLEEPING_FUND_CODE
)

def test_basic_functionality():
    """测试基本功能"""
    try:
        print("=" * 60)
        print("激进策略基本功能测试")
        print("=" * 60)
        
        # 1. 测试数据库初始化
        print("\n1. 测试数据库初始化...")
        init_database()
        print("✓ 数据库初始化成功")
        
        # 2. 测试策略状态加载
        print("\n2. 测试策略状态加载...")
        load_strategy_status()
        print("✓ 策略状态加载成功")
        
        # 3. 测试获取最新信号
        print("\n3. 测试获取最新信号...")
        latest_signal = get_latest_signal_from_db()
        if latest_signal:
            print(f"✓ 获取到最新信号：{latest_signal['signal_type']} - {latest_signal['signal_date']}")
        else:
            print("✓ 暂无历史信号记录")
        
        # 4. 显示配置信息
        print("\n4. 激进策略配置信息：")
        print(f"   总投资金额：{TOTAL_INVESTMENT_AMOUNT:,}元")
        print(f"   激活期基金：{ACTIVE_FUND_CODE}")
        print(f"   沉睡期基金：{SLEEPING_FUND_CODE}")
        
        # 5. 测试信号状态转换逻辑
        print("\n5. 测试信号状态转换逻辑...")
        test_signal_transitions(latest_signal)
        
        print("\n" + "=" * 60)
        print("✓ 激进策略基本功能测试完成！")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n✗ 测试失败：{str(e)}")
        import traceback
        print("详细错误信息：")
        print(traceback.format_exc())
        return False


def test_signal_transitions(latest_signal):
    """测试信号状态转换逻辑"""
    try:
        previous_signal_type = latest_signal['signal_type'] if latest_signal else None
        
        print(f"   当前历史信号：{previous_signal_type}")
        
        # 模拟各种信号转换场景
        scenarios = [
            (None, True, False, "无信号 -> buy"),
            ('EXITLONG', True, False, "sell -> buy"), 
            ('ENTERLONG', True, False, "buy -> buy"),
            (None, False, True, "无信号 -> sell"),
            ('ENTERLONG', False, True, "buy -> sell"),
            ('EXITLONG', False, True, "sell -> sell"),
            (None, False, False, "无信号 -> 无信号")
        ]
        
        for prev_signal, has_buy, has_sell, description in scenarios:
            action = determine_action(prev_signal, has_buy, has_sell)
            print(f"   {description}: {action}")
            
    except Exception as e:
        print(f"   信号转换测试失败：{str(e)}")


def determine_action(previous_signal_type, current_buy_signal, current_sell_signal):
    """确定应该执行的操作"""
    if previous_signal_type is None and current_buy_signal:
        return "全仓买入ACTIVE_FUND_CODE（现金+融资）"
    elif previous_signal_type == 'EXITLONG' and current_buy_signal:
        return "卖出SLEEPING_FUND_CODE，买入ACTIVE_FUND_CODE"
    elif previous_signal_type == 'ENTERLONG' and current_buy_signal:
        return "不操作（已在激活期）"
    elif previous_signal_type is None and current_sell_signal:
        return "全仓买入SLEEPING_FUND_CODE（仅现金）"
    elif previous_signal_type == 'ENTERLONG' and current_sell_signal:
        return "卖出ACTIVE_FUND_CODE，买入SLEEPING_FUND_CODE（仅现金）"
    elif previous_signal_type == 'EXITLONG' and current_sell_signal:
        return "不操作（已在沉睡期）"
    else:
        return "无操作"


if __name__ == "__main__":
    test_basic_functionality()
