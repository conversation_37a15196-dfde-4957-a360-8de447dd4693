# -*- coding: utf-8 -*-
"""
验证所有log_message调用是否都已更新完成
"""

import re

def verify_all_logs_updated():
    """验证所有log_message调用是否都已更新"""
    
    # 读取文件
    with open('value_averaging_strategy.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找所有log_message调用
    all_log_calls = re.findall(r'log_message\([^)]*\)', content)
    
    print(f"总共找到 {len(all_log_calls)} 个log_message调用")
    
    # 统计有ContextInfo的调用
    with_context = 0
    without_context = 0
    function_definitions = 0
    
    for call in all_log_calls:
        if 'def log_message(' in call:
            function_definitions += 1
        elif 'ContextInfo' in call:
            with_context += 1
        else:
            without_context += 1
            print(f"没有ContextInfo的调用: {call}")
    
    print(f"\n统计结果:")
    print(f"- 函数定义: {function_definitions}")
    print(f"- 有ContextInfo的调用: {with_context}")
    print(f"- 没有ContextInfo的调用: {without_context}")
    
    # 检查是否有真正缺少ContextInfo的调用（排除函数定义和注释）
    lines = content.split('\n')
    missing_calls = []
    
    for i, line in enumerate(lines, 1):
        # 跳过注释行
        if line.strip().startswith('#'):
            continue
            
        # 跳过函数定义行
        if 'def log_message(' in line:
            continue
            
        # 查找log_message调用
        if 'log_message(' in line and 'ContextInfo' not in line:
            missing_calls.append((i, line.strip()))
    
    print(f"\n真正缺少ContextInfo的调用: {len(missing_calls)}")
    for line_num, line_content in missing_calls:
        print(f"第{line_num}行: {line_content}")
    
    return len(missing_calls) == 0

def main():
    """主函数"""
    print("验证所有log_message调用是否都已更新完成...")
    print("=" * 50)
    
    try:
        is_complete = verify_all_logs_updated()
        
        if is_complete:
            print(f"\n🎉 完美！所有log_message调用都已成功更新！")
            print("✅ 所有调用都包含ContextInfo参数")
            print("✅ 现在可以正确记录K线时间了")
            print("✅ 回测模式下的日志查询将完全正常工作")
            
            print(f"\n立即可用的功能:")
            print("- 按K线时间查询日志")
            print("- 区分回测/实盘日志")
            print("- 完整的策略执行时序分析")
            
        else:
            print(f"\n⚠️  还有少量调用需要手动修复")
            
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
