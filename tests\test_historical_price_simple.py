#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试历史最高价获取功能的修复逻辑
不依赖pandas，专注于验证修复思路
"""

import datetime

def test_indexing_logic():
    """测试索引逻辑修复思路"""
    print("=== 测试索引逻辑修复思路 ===")
    
    # 模拟pandas DataFrame的行为
    class MockSeries:
        def __init__(self, data, index):
            self.data = data
            self.index = index
        
        def idxmax(self):
            """返回最大值对应的索引（不是位置）"""
            max_value = max(self.data)
            max_position = self.data.index(max_value)
            return self.index[max_position]  # 返回索引值，不是位置
        
        def loc(self, idx):
            """根据索引值获取数据"""
            try:
                position = self.index.index(idx)
                return self.data[position]
            except ValueError:
                raise KeyError(f"Index {idx} not found")
    
    class MockDataFrame:
        def __init__(self, data, index):
            self.data = data
            self.index = index
        
        def __getitem__(self, column):
            return MockSeries(self.data[column], self.index)
    
    # 创建模拟数据
    dates = [
        datetime.datetime(2020, 1, 31),
        datetime.datetime(2020, 2, 29),
        datetime.datetime(2020, 3, 31),
        datetime.datetime(2020, 4, 30),
        datetime.datetime(2020, 5, 31),
    ]
    
    prices = [1.2, 1.5, 2.8, 1.8, 1.3]  # 最高价在第3个月
    
    monthly_data = MockDataFrame({
        'close': prices,
        'high': [p * 1.02 for p in prices]
    }, dates)
    
    print(f"模拟数据：")
    for i, (date, price) in enumerate(zip(dates, prices)):
        print(f"  {date.strftime('%Y-%m-%d')}: {price}")
    
    # 测试修复后的逻辑
    try:
        print("\n--- 测试修复后的逻辑 ---")
        
        # 找到最高价及其对应的日期
        close_data = monthly_data['close']
        max_high_idx = close_data.idxmax()  # 这返回的是索引值（日期），不是位置
        
        print(f"idxmax()返回的索引类型：{type(max_high_idx)}")
        print(f"idxmax()返回的值：{max_high_idx}")
        
        # 直接使用索引值获取价格和日期
        max_high_price = close_data.loc(max_high_idx)  # 使用.loc而不是.iloc
        max_high_date = max_high_idx  # idxmax()返回的就是索引值（日期）
        
        print(f"最高价：{max_high_price}")
        print(f"最高价日期：{max_high_date}")
        
        # 格式化日期
        if hasattr(max_high_date, 'strftime'):
            date_str = max_high_date.strftime("%Y-%m-%d")
        else:
            date_str = str(max_high_date)
        
        print(f"格式化后的日期：{date_str}")
        
        # 验证结果
        assert max_high_price == 2.8, f"最高价应为2.8，实际为{max_high_price}"
        assert date_str == "2020-03-31", f"最高价日期应为2020-03-31，实际为{date_str}"
        
        print("✅ 修复后的逻辑测试通过")
        
    except Exception as e:
        print(f"❌ 测试失败：{str(e)}")
        import traceback
        traceback.print_exc()

def test_original_problem():
    """演示原始问题"""
    print("\n=== 演示原始问题 ===")
    
    # 模拟原始的错误逻辑
    class MockSeriesOld:
        def __init__(self, data, index):
            self.data = data
            self.index = index
        
        def idxmax(self):
            """返回最大值对应的索引（日期对象）"""
            max_value = max(self.data)
            max_position = self.data.index(max_value)
            return self.index[max_position]  # 返回日期对象
        
        def iloc(self, idx):
            """根据位置获取数据 - 这里会出错"""
            # 如果idx是日期对象而不是整数位置，就会出错
            if isinstance(idx, datetime.datetime):
                raise TypeError(f"cannot do positional indexing with {type(idx)} indexer {idx}")
            return self.data[idx]
    
    class MockDataFrameOld:
        def __init__(self, data, index):
            self.data = data
            self.index = index
        
        def __getitem__(self, column):
            return MockSeriesOld(self.data[column], self.index)
    
    # 创建模拟数据
    dates = [
        datetime.datetime(2020, 1, 31),
        datetime.datetime(2020, 2, 29),
        datetime.datetime(2020, 3, 31),
    ]
    
    prices = [1.2, 1.5, 2.8]
    
    monthly_data_old = MockDataFrameOld({
        'close': prices
    }, dates)
    
    # 演示原始的错误逻辑
    try:
        print("--- 演示原始错误逻辑 ---")
        
        close_data = monthly_data_old['close']
        max_high_idx = close_data.idxmax()  # 返回日期对象
        
        print(f"idxmax()返回：{max_high_idx} (类型：{type(max_high_idx)})")
        
        # 这里会出错：试图用日期对象做位置索引
        max_high_price = close_data.iloc(max_high_idx)  # 错误！
        
        print(f"不应该到达这里")
        
    except TypeError as e:
        print(f"✓ 成功重现原始错误：{str(e)}")
    except Exception as e:
        print(f"其他错误：{str(e)}")

def test_fix_explanation():
    """解释修复方案"""
    print("\n=== 修复方案解释 ===")
    
    print("原始问题：")
    print("1. idxmax() 返回的是索引值（日期），不是位置")
    print("2. 使用 iloc[日期对象] 试图做位置索引，导致错误")
    print("3. 使用 index[日期对象] 也会出错，因为这是位置索引")
    
    print("\n修复方案：")
    print("1. idxmax() 返回的就是我们要的日期")
    print("2. 使用 loc[日期对象] 根据索引值获取数据")
    print("3. 不需要再通过 index[...] 获取日期")
    
    print("\n修复前的代码：")
    print("  max_high_idx = monthly_data['close'].idxmax()")
    print("  max_high_price = close_data.iloc[max_high_idx]  # 错误！")
    print("  max_high_date = monthly_data.index[max_high_idx]  # 错误！")
    
    print("\n修复后的代码：")
    print("  max_high_idx = close_data.idxmax()")
    print("  max_high_price = close_data.loc[max_high_idx]  # 正确！")
    print("  max_high_date = max_high_idx  # 正确！")

if __name__ == "__main__":
    test_indexing_logic()
    test_original_problem()
    test_fix_explanation()
    print("\n🎉 历史价格修复逻辑验证完成！")
