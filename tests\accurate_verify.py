# -*- coding: utf-8 -*-
"""
准确验证所有log_message调用是否都已更新完成
"""

import re

def accurate_verify():
    """准确验证所有log_message调用"""
    
    # 读取文件
    with open('value_averaging_strategy.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 使用更准确的正则表达式查找log_message调用
    # 匹配完整的log_message调用，包括多行
    pattern = r'log_message\s*\([^)]*?\)'
    matches = re.findall(pattern, content, re.DOTALL)
    
    print(f"总共找到 {len(matches)} 个log_message调用")
    
    # 统计
    with_context = 0
    without_context = 0
    function_definitions = 0
    
    missing_calls = []
    
    for match in matches:
        if 'def log_message(' in match:
            function_definitions += 1
        elif 'ContextInfo' in match:
            with_context += 1
        else:
            without_context += 1
            missing_calls.append(match.replace('\n', ' ').strip())
    
    print(f"\n统计结果:")
    print(f"- 函数定义: {function_definitions}")
    print(f"- 有ContextInfo的调用: {with_context}")
    print(f"- 没有ContextInfo的调用: {without_context}")
    
    if missing_calls:
        print(f"\n缺少ContextInfo的调用:")
        for i, call in enumerate(missing_calls, 1):
            print(f"{i}. {call}")
    
    return len(missing_calls) == 0

def main():
    """主函数"""
    print("准确验证所有log_message调用是否都已更新完成...")
    print("=" * 60)
    
    try:
        is_complete = accurate_verify()
        
        if is_complete:
            print(f"\n🎉 完美！所有log_message调用都已成功更新！")
            print("✅ 所有调用都包含ContextInfo参数")
            print("✅ 现在可以正确记录K线时间了")
            print("✅ 回测模式下的日志查询将完全正常工作")
            
            print(f"\n🚀 立即可用的功能:")
            print("- 按K线时间查询日志")
            print("- 区分回测/实盘日志")
            print("- 完整的策略执行时序分析")
            print("- 价值平均策略的详细执行记录")
            
            print(f"\n📊 查询示例:")
            print("-- 查询2024年8月30日的价值平均策略执行")
            print("SELECT * FROM trade_logs WHERE DATE(kline_date) = '2024-08-30' AND operation LIKE '%价值平均%';")
            print()
            print("-- 查询回测期间的交易执行记录")
            print("SELECT * FROM trade_logs WHERE operation = '交易执行' AND is_backtest = 1 ORDER BY kline_date;")
            
        else:
            print(f"\n⚠️  还有少量调用需要手动修复")
            
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
