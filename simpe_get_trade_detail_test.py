# encoding:gbk
"""
简单获取账户明细信息测试
"""
from typing import Dict, List, Tuple, Optional

ACCOUNT_ID = '************'
ACCOUNT_TYPE = 'CREDIT'

def init(ContextInfo):
    ContextInfo.set_account(ACCOUNT_ID)

def handlebar(ContextInfo):
    if ContextInfo.is_last_bar():
        # get_trade_details(ContextInfo, 'POSITION')
        get_trade_details(ContextInfo, 'ORDER')
        # get_trade_details(ContextInfo, 'DEAL')
        # get_trade_details(ContextInfo, 'ACCOUNT')
        # get_trade_details(ContextInfo, 'TASK')

def get_trade_details(ContextInfo, dataType):
    details = get_trade_detail_data(ACCOUNT_ID, ACCOUNT_TYPE, dataType)
    for d in details:
        print('### 对象明细打印 ###')
        for attr in dir(d):
            try:
                value = getattr(d, attr)
                if not callable(value):
                    print(f"   {attr} = {value}")
            except:
                pass
    print('### 打印结束 ###')