# -*- coding: utf-8 -*-
"""
简单的重采样测试
"""

import pandas as pd
import numpy as np

def simple_test():
    print("=== 简单重采样测试 ===")
    
    # 创建简单的日线数据
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    daily_data = pd.DataFrame({
        'open': [10.0] * len(dates),
        'high': [10.5] * len(dates),
        'low': [9.5] * len(dates),
        'close': [10.0] * len(dates),
    }, index=dates)
    
    print(f"日线数据: {len(daily_data)} 条")
    print(f"日期范围: {daily_data.index[0]} 到 {daily_data.index[-1]}")
    
    # 测试季线重采样
    quarterly = daily_data.resample('Q').agg({
        'open': 'first',
        'high': 'max',
        'low': 'min',
        'close': 'last'
    }).dropna()
    
    print(f"季线数据: {len(quarterly)} 条")
    print("季线数据:")
    print(quarterly)
    
    # 测试月线重采样
    monthly = daily_data.resample('M').agg({
        'open': 'first',
        'high': 'max',
        'low': 'min',
        'close': 'last'
    }).dropna()
    
    print(f"月线数据: {len(monthly)} 条")
    print("月线数据前5条:")
    print(monthly.head())

if __name__ == "__main__":
    simple_test()
