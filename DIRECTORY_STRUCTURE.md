# 目录结构说明

## 📁 根目录 - 核心文件
```
├── value_averaging_strategy.py          # 🎯 主策略：价值平均+择时量化投资
├── simple_trading_test.py               # 🧪 简单交易测试策略  
├── simple_ma_crossover_strategy.py      # 📈 移动平均线策略
├── 价值平均策略实现分析.md                # 📋 策略分析文档
├── 回测模式使用说明.md                   # 📖 回测使用指南
├── 回测适配方案.md                      # 🔧 回测技术方案
├── 简单移动平均线策略使用说明.md          # 📘 MA策略说明
└── README.md                           # 📄 项目总览
```

## 📁 /tests/ - 测试脚本 (40+ 文件)
```
├── test_*.py                           # 🧪 单元测试脚本
├── verify_*.py                         # ✅ 验证脚本
├── debug_*.py                          # 🐛 调试工具
├── simple_*_test.py                    # 🔬 简单测试
└── analyze_*.py                        # 📊 分析工具
```

## 📁 /tools/ - 工具脚本 (8 文件)
```
├── fix_*.py                            # 🔧 修复工具
├── batch_*.py                          # 📦 批量处理
├── update_*.py                         # 🔄 更新脚本
└── pandas_fix.py                       # 🐼 Pandas兼容性修复
```

## 📁 /database/ - 数据库相关 (3 文件)
```
├── gytrading2.db                       # 🗄️ SQLite数据库文件
├── init_database_only.py               # 🏗️ 数据库初始化
└── check_error_logs.py                 # 📋 错误日志检查
```

## 📁 /archive/ - 历史文档 (20+ 文件)
```
├── *_summary.md                        # 📝 开发总结
├── *_fix*.md                           # 🔧 修复记录  
├── *_update*.md                        # 🔄 升级说明
├── *_analysis.md                       # 📊 技术分析
└── README.md                           # 📄 旧版README
```

## 📁 /docs/ - 项目文档 (9+ 文件)
```
├── 开发需求说明.md                      # 📋 需求文档
├── 数据库设计.md                       # 🗄️ 数据库设计
├── 策略流程图.md                       # 📊 流程图
├── 交易任务队列系统*.md                 # 🔄 队列系统文档
└── iQuant资料/                         # 📚 平台资料
```

## 🎯 文件分类逻辑

### ✅ 保留在根目录
- **核心策略文件**：主要的交易策略实现
- **重要文档**：策略分析、使用说明等关键文档
- **项目说明**：README等项目概览文件

### 📦 移动到子目录
- **测试文件** → `/tests/`：所有test_*.py、verify_*.py等
- **工具脚本** → `/tools/`：修复、批处理、更新工具
- **数据库** → `/database/`：数据库文件和相关脚本
- **历史文档** → `/archive/`：开发过程中的总结、修复记录
- **项目文档** → `/docs/`：设计文档、需求说明等

## 📈 整理效果

### 整理前
- 根目录70+个文件，混乱不堪 😵
- 难以找到核心文件
- 测试文件与主文件混杂

### 整理后  
- 根目录仅8个核心文件 ✨
- 清晰的目录结构
- 便于维护和查找

## 🔍 快速定位

- **要运行策略** → 根目录的 `.py` 文件
- **要查看文档** → 根目录的 `.md` 文件或 `/docs/`
- **要运行测试** → `/tests/` 目录
- **要使用工具** → `/tools/` 目录
- **要查看历史** → `/archive/` 目录
- **要管理数据库** → `/database/` 目录

现在根目录清爽多了！🎉
