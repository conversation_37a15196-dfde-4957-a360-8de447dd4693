# -*- coding: utf-8 -*-

"""
测试交易时点控制功能
"""

import datetime
import sys
import os

# 添加当前目录到路径，以便导入策略模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_time_control_function():
    """测试时点控制函数"""
    print("🧪 测试交易时点控制函数")
    
    # 模拟时点控制函数（从策略文件中提取）
    def is_trade_time_allowed_test(current_time_str, trade_time_control, enable_time_control=True, is_backtest=False):
        """
        测试版本的时点控制函数
        
        Args:
            current_time_str: 当前时间字符串 (HHmmss)
            trade_time_control: 交易时点控制 (HHmmss)
            enable_time_control: 是否启用时点控制
            is_backtest: 是否为回测模式
            
        Returns:
            tuple: (是否允许交易, 说明信息)
        """
        try:
            # 回测模式忽略时点控制
            if is_backtest:
                return (True, "回测模式，忽略时点控制")
            
            # 检查是否启用时点控制
            if not enable_time_control:
                return (True, "时点控制已禁用")
            
            # 解析设置的交易时点
            if len(trade_time_control) != 6:
                return (True, f"时点格式错误：{trade_time_control}，允许交易")
            
            # 比较时间字符串
            if current_time_str >= trade_time_control:
                return (True, f"当前时间{current_time_str[:2]}:{current_time_str[2:4]}:{current_time_str[4:6]}已过交易时点{trade_time_control[:2]}:{trade_time_control[2:4]}:{trade_time_control[4:6]}")
            else:
                return (False, f"当前时间{current_time_str[:2]}:{current_time_str[2:4]}:{current_time_str[4:6]}未到交易时点{trade_time_control[:2]}:{trade_time_control[2:4]}:{trade_time_control[4:6]}")
                
        except Exception as e:
            return (True, f"时点检查异常，允许交易：{str(e)}")
    
    # 测试用例
    test_cases = [
        # (当前时间, 交易时点, 启用控制, 回测模式, 期望结果, 测试描述)
        ("143000", "144500", True, False, False, "当前14:30:00，交易时点14:45:00，未到时点"),
        ("144500", "144500", True, False, True, "当前14:45:00，交易时点14:45:00，刚好到时点"),
        ("150000", "144500", True, False, True, "当前15:00:00，交易时点14:45:00，已过时点"),
        ("143000", "144500", False, False, True, "时点控制禁用，应该允许交易"),
        ("143000", "144500", True, True, True, "回测模式，应该忽略时点控制"),
        ("143000", "1445", True, False, True, "时点格式错误，应该允许交易"),
    ]
    
    print("📊 测试结果：")
    all_passed = True
    
    for i, (current_time, trade_time, enable_control, is_backtest, expected_allowed, description) in enumerate(test_cases, 1):
        allowed, reason = is_trade_time_allowed_test(current_time, trade_time, enable_control, is_backtest)
        
        if allowed == expected_allowed:
            status = "✅ 通过"
        else:
            status = "❌ 失败"
            all_passed = False
        
        print(f"   测试{i}: {status}")
        print(f"      描述: {description}")
        print(f"      结果: {allowed}, 原因: {reason}")
        print()
    
    if all_passed:
        print("🎉 所有时点控制测试通过！")
        return True
    else:
        print("⚠️  部分时点控制测试失败")
        return False

def test_integration_scenario():
    """测试集成场景"""
    print("\n🧪 测试集成场景")
    
    # 模拟一天的交易场景
    scenarios = [
        {
            "time": "093000",  # 09:30:00 开盘
            "trade_time": "144500",  # 14:45:00 交易时点
            "description": "开盘时检查，未到交易时点",
            "expected": False
        },
        {
            "time": "120000",  # 12:00:00 中午
            "trade_time": "144500",  # 14:45:00 交易时点
            "description": "中午时检查，未到交易时点",
            "expected": False
        },
        {
            "time": "144500",  # 14:45:00 交易时点
            "trade_time": "144500",  # 14:45:00 交易时点
            "description": "交易时点检查，允许交易",
            "expected": True
        },
        {
            "time": "150000",  # 15:00:00 收盘
            "trade_time": "144500",  # 14:45:00 交易时点
            "description": "收盘时检查，已过交易时点",
            "expected": True
        }
    ]
    
    print("📊 集成场景测试结果：")
    all_passed = True
    
    for i, scenario in enumerate(scenarios, 1):
        # 模拟时点检查
        current_time = scenario["time"]
        trade_time = scenario["trade_time"]
        
        allowed = current_time >= trade_time
        expected = scenario["expected"]
        
        if allowed == expected:
            status = "✅ 通过"
        else:
            status = "❌ 失败"
            all_passed = False
        
        time_display = f"{current_time[:2]}:{current_time[2:4]}:{current_time[4:6]}"
        trade_time_display = f"{trade_time[:2]}:{trade_time[2:4]}:{trade_time[4:6]}"
        
        print(f"   场景{i}: {status}")
        print(f"      时间: {time_display}, 交易时点: {trade_time_display}")
        print(f"      描述: {scenario['description']}")
        print(f"      结果: {'允许交易' if allowed else '禁止交易'}")
        print()
    
    if all_passed:
        print("🎉 所有集成场景测试通过！")
        return True
    else:
        print("⚠️  部分集成场景测试失败")
        return False

def test_real_time_scenario():
    """测试实时场景"""
    print("\n🧪 测试实时场景")
    
    # 获取当前时间
    now = datetime.datetime.now()
    current_time_str = now.strftime("%H%M%S")
    
    # 设置几个测试时点
    test_trade_times = [
        "093000",  # 09:30:00
        "120000",  # 12:00:00
        "144500",  # 14:45:00
        "150000",  # 15:00:00
    ]
    
    print(f"📊 当前时间: {now.strftime('%H:%M:%S')} ({current_time_str})")
    print("测试不同交易时点的控制效果：")
    
    for trade_time in test_trade_times:
        allowed = current_time_str >= trade_time
        trade_time_display = f"{trade_time[:2]}:{trade_time[2:4]}:{trade_time[4:6]}"
        
        status = "✅ 允许" if allowed else "❌ 禁止"
        print(f"   交易时点 {trade_time_display}: {status}")
    
    print("\n💡 建议:")
    print("   - 如果当前是交易时间，可以设置一个合适的交易时点")
    print("   - 建议设置在14:45:00左右，避免尾盘波动")
    print("   - 可以根据实际需要调整 TRADE_TIME_CONTROL 参数")

def show_configuration_example():
    """显示配置示例"""
    print("\n📋 配置示例")
    
    print("在 value_averaging_strategy.py 中的配置：")
    print("""
# 交易时点控制参数
TRADE_TIME_CONTROL = "144500"   # 交易时点控制：HHmmss格式，如144500代表14:45:00
ENABLE_TIME_CONTROL = True      # 是否启用时点控制（实盘模式有效，回测模式忽略）
""")
    
    print("常用时点设置建议：")
    print("   - 093000: 开盘时交易（09:30:00）")
    print("   - 113000: 上午收盘前交易（11:30:00）")
    print("   - 130000: 下午开盘时交易（13:00:00）")
    print("   - 144500: 下午中段交易（14:45:00）- 推荐")
    print("   - 145500: 接近收盘交易（14:55:00）")
    
    print("\n⚠️  注意事项：")
    print("   - 时点控制只在实盘模式生效，回测模式会忽略")
    print("   - 设置 ENABLE_TIME_CONTROL = False 可以禁用时点控制")
    print("   - 时点格式必须是6位数字：HHmmss")
    print("   - 建议避开开盘和收盘的极端时间点")

def main():
    """主测试函数"""
    print("🚀 开始测试交易时点控制功能\n")
    
    # 测试1：时点控制函数
    test1_result = test_time_control_function()
    
    # 测试2：集成场景
    test2_result = test_integration_scenario()
    
    # 测试3：实时场景
    test_real_time_scenario()
    
    # 显示配置示例
    show_configuration_example()
    
    # 总结
    print(f"\n📋 测试总结：")
    print(f"   时点控制函数：{'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"   集成场景测试：{'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 交易时点控制功能测试全部通过！")
        print("\n✨ 功能特点：")
        print("   1. 支持精确到秒的时点控制")
        print("   2. 实盘模式生效，回测模式忽略")
        print("   3. 可以通过参数启用/禁用")
        print("   4. 未到时点会记录日志并跳过交易")
        print("   5. 到达时点后当天只允许交易一次")
    else:
        print("\n⚠️  部分测试失败，需要进一步检查。")

if __name__ == "__main__":
    main()
