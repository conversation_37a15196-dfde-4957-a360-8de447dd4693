# -*- coding: utf-8 -*-
"""
测试任务拆分和重试功能
"""

import sys
import os
import sqlite3
import datetime
import uuid
import json

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入策略模块
from value_averaging_strategy import (
    TradeTaskQueue, TaskType, TaskStatus,
    init_database, g_db_connection,
    MAX_TRADE_SHARES, MAX_RETRY_COUNT, RETRY_INTERVAL
)

# 设置必要的全局变量
import value_averaging_strategy
value_averaging_strategy.g_current_bar_time = datetime.datetime.now()
value_averaging_strategy.g_is_backtest = False

class MockContextInfo:
    """模拟ContextInfo对象"""
    def __init__(self):
        self.barpos = 0

    def get_market_data_ex(self, *args, **kwargs):
        # 返回模拟价格数据
        return {
            "159915.SZ": [{'close': 1.5}],
            "510720.SH": [{'close': 2.0}]
        }

    def get_bar_timetag(self, barpos):
        """模拟获取K线时间戳"""
        return int(datetime.datetime.now().timestamp())

    def get_full_tick(self, stock_code):
        """模拟获取tick数据"""
        return [1.5]  # 返回模拟价格

def get_current_price(stock_code, ContextInfo):
    """模拟获取当前价格"""
    if "159915" in stock_code:
        return 1.5
    elif "510720" in stock_code:
        return 2.0
    return 1.0

def test_task_split():
    """测试任务拆分功能"""
    print("=" * 60)
    print("测试任务拆分功能")
    print("=" * 60)
    
    try:
        # 1. 初始化数据库
        print("1. 初始化数据库...")
        init_database()
        print("数据库初始化完成")
        
        # 2. 创建任务队列管理器
        print("2. 创建任务队列管理器...")
        task_queue = TradeTaskQueue()
        context_info = MockContextInfo()
        
        # 3. 测试不需要拆分的任务
        print("3. 测试不需要拆分的任务（500股）...")
        task_group_id = str(uuid.uuid4())
        
        task_list = task_queue.create_task_with_split(
            task_group_id=task_group_id,
            task_type=TaskType.BUY_159915_CASH.value,
            stock_code="159915.SZ",
            target_shares=500,  # 小于MAX_TRADE_SHARES
            ContextInfo=context_info
        )
        
        print(f"创建了{len(task_list)}个任务")
        assert len(task_list) == 1, "不需要拆分的任务应该只创建1个任务"
        print("✓ 不拆分任务测试通过")
        
        # 4. 测试需要拆分的任务
        print(f"4. 测试需要拆分的任务（{MAX_TRADE_SHARES + 500}股）...")
        task_group_id2 = str(uuid.uuid4())
        
        large_shares = MAX_TRADE_SHARES + 500
        task_list2 = task_queue.create_task_with_split(
            task_group_id=task_group_id2,
            task_type=TaskType.SELL_159915.value,
            stock_code="159915.SZ",
            target_shares=large_shares,
            ContextInfo=context_info
        )
        
        expected_tasks = 2  # 应该拆分为2个任务
        print(f"创建了{len(task_list2)}个任务，预期{expected_tasks}个")
        assert len(task_list2) == expected_tasks, f"应该拆分为{expected_tasks}个任务"
        
        # 5. 验证拆分任务的依赖关系
        print("5. 验证拆分任务的依赖关系...")

        # 确保数据库连接存在
        if g_db_connection is None:
            print("数据库连接为空，重新初始化...")
            init_database()

        cursor = g_db_connection.cursor()
        
        # 查询任务详情
        task_ids = [task[0] for task in task_list2]
        cursor.execute(f"""
            SELECT id, target_shares, depends_on_task, task_params
            FROM trade_task_queue 
            WHERE id IN ({','.join(['?'] * len(task_ids))})
            ORDER BY id
        """, task_ids)
        
        tasks = cursor.fetchall()
        print(f"查询到{len(tasks)}个任务详情")
        
        # 验证第一个任务
        first_task = tasks[0]
        assert first_task[1] == MAX_TRADE_SHARES, f"第一个任务应该是{MAX_TRADE_SHARES}股"
        assert first_task[2] is None, "第一个任务不应该有依赖"
        
        # 验证第二个任务
        second_task = tasks[1]
        assert second_task[1] == 500, "第二个任务应该是500股"
        assert second_task[2] == str(first_task[0]), "第二个任务应该依赖第一个任务"
        
        # 验证任务参数
        second_task_params = json.loads(second_task[3]) if second_task[3] else {}
        assert second_task_params.get('is_split_task') == True, "应该标记为拆分任务"
        assert second_task_params.get('split_index') == 2, "应该是第2个拆分任务"
        assert second_task_params.get('total_splits') == 2, "总共应该有2个拆分任务"
        
        print("✓ 任务拆分测试通过")
        
        # 6. 测试极大数量的拆分
        print(f"6. 测试极大数量拆分（{MAX_TRADE_SHARES * 3 + 200}股）...")
        task_group_id3 = str(uuid.uuid4())
        
        huge_shares = MAX_TRADE_SHARES * 3 + 200
        task_list3 = task_queue.create_task_with_split(
            task_group_id=task_group_id3,
            task_type=TaskType.BUY_159915_CASH.value,
            stock_code="159915.SZ",
            target_shares=huge_shares,
            ContextInfo=context_info
        )
        
        expected_tasks3 = 4  # 应该拆分为4个任务
        print(f"创建了{len(task_list3)}个任务，预期{expected_tasks3}个")
        assert len(task_list3) == expected_tasks3, f"应该拆分为{expected_tasks3}个任务"
        
        print("✓ 极大数量拆分测试通过")
        
        print("\n" + "=" * 60)
        print("所有任务拆分测试通过！")
        print("=" * 60)
        
    except Exception as e:
        print(f"测试失败：{str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def test_retry_mechanism():
    """测试重试机制"""
    print("=" * 60)
    print("测试重试机制")
    print("=" * 60)
    
    try:
        # 这里可以添加重试机制的测试
        # 由于重试机制涉及时间间隔，这里只做基本的数据库操作测试
        
        task_queue = TradeTaskQueue()
        context_info = MockContextInfo()
        
        # 创建一个测试任务
        task_group_id = str(uuid.uuid4())
        task_list = task_queue.create_task_with_split(
            task_group_id=task_group_id,
            task_type=TaskType.BUY_159915_CASH.value,
            stock_code="159915.SZ",
            target_shares=100,
            ContextInfo=context_info
        )
        
        task_id, order_uuid = task_list[0]
        
        # 模拟任务失败和重试计数
        from value_averaging_strategy import g_trade_task_executor
        
        print(f"测试任务ID: {task_id}, UUID: {order_uuid}")
        
        # 测试获取重试次数
        retry_count = g_trade_task_executor.get_current_retry_count(order_uuid)
        print(f"初始重试次数: {retry_count}")
        assert retry_count == 0, "初始重试次数应该为0"
        
        # 测试增加重试次数
        g_trade_task_executor.increment_retry_count(order_uuid)
        retry_count = g_trade_task_executor.get_current_retry_count(order_uuid)
        print(f"增加后重试次数: {retry_count}")
        assert retry_count == 1, "增加后重试次数应该为1"
        
        print("✓ 重试机制基本测试通过")
        
        print("\n" + "=" * 60)
        print("重试机制测试完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"重试机制测试失败：{str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    print("开始测试任务拆分和重试功能...")
    
    # 测试任务拆分
    if test_task_split():
        print("✅ 任务拆分测试成功")
    else:
        print("❌ 任务拆分测试失败")
        sys.exit(1)
    
    # 测试重试机制
    if test_retry_mechanism():
        print("✅ 重试机制测试成功")
    else:
        print("❌ 重试机制测试失败")
        sys.exit(1)
    
    print("\n🎉 所有测试通过！")
