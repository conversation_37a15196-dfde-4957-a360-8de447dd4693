#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试K线日期记录功能
验证信号记录中包含正确的K线日期
"""

import sqlite3
import datetime
import os

def test_kline_date_format():
    """测试K线日期格式转换"""
    print("=== 测试K线日期格式转换 ===")
    
    # 模拟时间戳（毫秒）
    test_timestamp = 1691366400000  # 2023-08-07 00:00:00 UTC
    
    # 转换为日期时间
    kline_datetime = datetime.datetime.fromtimestamp(test_timestamp / 1000)
    kline_date = kline_datetime.strftime("%Y%m%d")
    
    print(f"时间戳: {test_timestamp}")
    print(f"日期时间: {kline_datetime}")
    print(f"格式化日期: {kline_date}")
    
    # 验证格式
    assert len(kline_date) == 8, f"日期格式长度应为8位，实际为{len(kline_date)}"
    assert kline_date.isdigit(), f"日期应为纯数字，实际为{kline_date}"
    assert kline_date.startswith("20"), f"日期应以20开头，实际为{kline_date}"
    
    print("✅ K线日期格式转换测试通过")

def test_database_kline_date_storage():
    """测试数据库中K线日期的存储"""
    print("\n=== 测试数据库K线日期存储 ===")
    
    # 创建测试数据库
    if os.path.exists('test_kline_date.db'):
        os.remove('test_kline_date.db')
    
    conn = sqlite3.connect('test_kline_date.db')
    cursor = conn.cursor()
    
    # 创建表
    cursor.execute("""
        CREATE TABLE signal_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            signal_date TEXT NOT NULL,
            signal_type TEXT NOT NULL,
            signal_price REAL NOT NULL,
            ema_value REAL NOT NULL,
            bottom_line REAL,
            top_line REAL,
            kline_position INTEGER,
            kline_date TEXT,
            is_valid INTEGER NOT NULL,
            filter_reason TEXT,
            created_time TEXT NOT NULL
        )
    """)
    
    # 插入测试数据
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    test_kline_date = "20250807"
    
    cursor.execute("""
        INSERT INTO signal_history
        (signal_date, signal_type, signal_price, ema_value, kline_position, 
         kline_date, is_valid, created_time)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    """, (current_time, 'ENTERLONG', 10.0, 10.5, 100, test_kline_date, 1, current_time))
    
    conn.commit()
    
    # 查询验证
    cursor.execute("SELECT kline_date, kline_position FROM signal_history WHERE id = 1")
    result = cursor.fetchone()
    
    assert result is not None, "应该能查询到记录"
    stored_kline_date, stored_kline_position = result
    
    print(f"存储的K线日期: {stored_kline_date}")
    print(f"存储的K线位置: {stored_kline_position}")
    
    assert stored_kline_date == test_kline_date, f"K线日期应为{test_kline_date}，实际为{stored_kline_date}"
    assert stored_kline_position == 100, f"K线位置应为100，实际为{stored_kline_position}"
    
    conn.close()
    
    # 清理
    if os.path.exists('test_kline_date.db'):
        os.remove('test_kline_date.db')
    
    print("✅ 数据库K线日期存储测试通过")

def test_signal_details_structure():
    """测试信号详情结构"""
    print("\n=== 测试信号详情结构 ===")
    
    # 模拟信号详情
    signal_details = {
        'signal_type': 'ENTERLONG',
        'signal_price': 10.0,
        'ema_value': 10.5,
        'bottom_line': 8.925,
        'top_line': None,
        'signal_time': '2025-08-07 10:30:00',
        'kline_position': 100,
        'kline_date': '20250807'
    }
    
    # 验证必要字段
    required_fields = ['signal_type', 'signal_price', 'ema_value', 'signal_time', 
                      'kline_position', 'kline_date']
    
    for field in required_fields:
        assert field in signal_details, f"信号详情应包含字段: {field}"
        assert signal_details[field] is not None, f"字段{field}不应为None"
    
    # 验证K线日期格式
    kline_date = signal_details['kline_date']
    assert len(kline_date) == 8, f"K线日期长度应为8，实际为{len(kline_date)}"
    assert kline_date.isdigit(), f"K线日期应为纯数字，实际为{kline_date}"
    
    print(f"信号详情: {signal_details}")
    print("✅ 信号详情结构测试通过")

if __name__ == "__main__":
    test_kline_date_format()
    test_database_kline_date_storage()
    test_signal_details_structure()
    print("\n🎉 所有K线日期相关测试通过！")
