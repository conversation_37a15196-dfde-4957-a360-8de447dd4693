# -*- coding: utf-8 -*-
"""
验证API修正的脚本
"""

print("=== API修正验证 ===")

# 检查代码中是否还有.empty的错误用法
def check_code_for_empty_usage():
    """检查代码中是否还有.empty的错误用法"""
    print("检查代码中的.empty用法...")
    
    try:
        with open('value_averaging_strategy.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 查找.empty的使用
        if '.empty' in content:
            print("❌ 发现.empty的错误用法")
            lines = content.split('\n')
            for i, line in enumerate(lines, 1):
                if '.empty' in line:
                    print(f"  第{i}行: {line.strip()}")
            return False
        else:
            print("✓ 未发现.empty的错误用法")
            return True
            
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
        return False

# 验证正确的API处理方式
def verify_correct_api_usage():
    """验证正确的API处理方式"""
    print("验证正确的API处理方式...")
    
    # 模拟get_market_data_ex的正确返回格式
    market_data = {
        '159915.SZ': {
            'close': [10.0, 10.1, 10.2, 10.3],
            'high': [10.1, 10.2, 10.3, 10.4],
            'open': [9.9, 10.0, 10.1, 10.2],
            'low': [9.8, 9.9, 10.0, 10.1]
        }
    }
    
    # 正确的处理方式
    if market_data is None or len(market_data) == 0:
        print("❌ 无市场数据")
        return False
    
    stock_code = '159915.SZ'
    stock_data = market_data.get(stock_code)
    if stock_data is None or len(stock_data) == 0:
        print("❌ 无股票数据")
        return False
    
    print("✓ API处理方式正确")
    print(f"  返回格式: 字典 {{{stock_code}: data}}")
    print(f"  数据检查: len(market_data) = {len(market_data)}")
    print(f"  股票数据: len(stock_data) = {len(stock_data)}")
    
    return True

def main():
    """主函数"""
    print("开始验证...")
    
    # 检查代码
    code_ok = check_code_for_empty_usage()
    
    # 验证API用法
    api_ok = verify_correct_api_usage()
    
    print("\n=== 验证结果 ===")
    if code_ok and api_ok:
        print("✓ 所有检查通过，API修正成功")
        print("✓ 代码现在符合技术文档规格")
    else:
        print("❌ 仍有问题需要修正")
    
    print("验证完成")

if __name__ == "__main__":
    main()
