# -*- coding: utf-8 -*-
"""
价值平均+择时策略测试文件
用于测试策略各模块的功能正确性

作者：AI Assistant
创建时间：2025-01-29
"""

import unittest
import sqlite3
import datetime
import os
import sys
from unittest.mock import Mock, patch, MagicMock

# 添加策略文件路径
sys.path.append('.')

# 导入策略模块
from value_averaging_strategy import *


class TestValueAveragingStrategy(unittest.TestCase):
    """价值平均策略测试类"""

    def setUp(self):
        """测试前准备"""
        # 使用测试数据库
        global DATABASE_PATH, g_db_connection, g_strategy_status
        DATABASE_PATH = "test_strategy.db"

        # 清理可能存在的测试数据库
        if os.path.exists(DATABASE_PATH):
            os.remove(DATABASE_PATH)

        # 重置全局变量
        g_db_connection = None
        g_strategy_status = None

        # 创建模拟的ContextInfo对象
        self.mock_context = Mock()
        self.mock_context.accid = "test_account"
        self.mock_context.is_last_bar.return_value = True
        self.mock_context.run_count = 0

        # 模拟市场数据 - 根据技术文档，get_market_data_ex返回字典格式 {code: data}
        self.mock_stock_data = Mock()
        self.mock_stock_data.__len__ = Mock(return_value=100)
        self.mock_stock_data.__getitem__ = Mock(return_value=[10.0] * 100)
        self.mock_stock_data.iloc = Mock()
        self.mock_stock_data.iloc.__getitem__ = Mock(return_value=10.0)

        # 模拟字典格式的返回值
        self.mock_market_data = {
            '159915.SZ': self.mock_stock_data  # SIGNAL_FUND_CODE
        }
        self.mock_context.get_market_data_ex.return_value = self.mock_market_data

    def tearDown(self):
        """测试后清理"""
        global g_db_connection

        # 关闭数据库连接
        if g_db_connection:
            g_db_connection.close()
            g_db_connection = None

        # 删除测试数据库
        if os.path.exists(DATABASE_PATH):
            os.remove(DATABASE_PATH)

    def test_database_initialization(self):
        """测试数据库初始化"""
        # 初始化数据库
        init_database()

        # 验证数据库连接
        self.assertIsNotNone(g_db_connection)

        # 验证表是否创建
        cursor = g_db_connection.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]

        expected_tables = [
            'strategy_status', 'signal_history', 'trade_orders',
            'position_records', 'skip_periods', 'trade_logs', 'account_info'
        ]

        for table in expected_tables:
            self.assertIn(table, tables, f"表 {table} 未创建")

    def test_strategy_status_management(self):
        """测试策略状态管理"""
        # 初始化数据库
        init_database()

        # 加载策略状态
        load_strategy_status()

        # 验证初始状态
        self.assertIsNotNone(g_strategy_status)
        self.assertEqual(g_strategy_status['current_phase'], 'sleeping')

        # 测试状态验证
        self.assertTrue(validate_strategy_state())

        # 测试状态更新
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        g_strategy_status['current_phase'] = 'active'
        update_strategy_status(current_time)

        # 重新加载验证
        load_strategy_status()
        self.assertEqual(g_strategy_status['current_phase'], 'active')

    def test_ema_calculation(self):
        """测试EMA计算"""
        # 测试数据
        prices = [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]
        period = 5

        # 计算EMA
        ema_values = calculate_ema(prices, period)

        # 验证结果
        self.assertGreater(len(ema_values), 0)
        self.assertEqual(len(ema_values), len(prices) - period + 1)

        # 验证EMA值递增（因为价格递增）
        for i in range(1, len(ema_values)):
            self.assertGreater(ema_values[i], ema_values[i-1])

    def test_signal_filter(self):
        """测试信号过滤"""
        # 初始化数据库
        init_database()

        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 第一个信号应该通过
        is_valid, reason = check_signal_filter('ENTERLONG', current_time)
        self.assertTrue(is_valid)
        self.assertIsNone(reason)

        # 记录信号到数据库
        signal_details = {
            'signal_type': 'ENTERLONG',
            'signal_price': 10.0,
            'ema_value': 9.5,
            'bottom_line': 8.5,
            'top_line': None,
            'signal_time': current_time
        }
        record_signal_to_db(signal_details, True, None)

        # 立即再次检查同类型信号，应该被过滤（简化测试）
        # 实际过滤逻辑基于时间间隔，这里只测试基本功能

    def test_value_averaging_calculation(self):
        """测试价值平均计算"""
        # 初始化数据库和状态
        init_database()
        load_strategy_status()

        # 设置激活期状态
        g_strategy_status['current_phase'] = 'active'
        g_strategy_status['start_period_date'] = '2024-01-01'
        g_strategy_status['start_period_price'] = 10.0

        # 测试价值平均计算
        current_price = 12.0
        result = calculate_value_averaging(current_price)

        # 验证结果结构
        self.assertIn('current_period', result)
        self.assertIn('target_amount', result)
        self.assertIn('current_value', result)
        self.assertIn('trade_amount', result)
        self.assertIn('trade_shares', result)
        self.assertIn('trade_type', result)

        # 验证期数大于0
        self.assertGreater(result['current_period'], 0)

    def test_current_period_calculation(self):
        """测试当前期数计算"""
        # 测试月线周期
        start_date = "2024-01-01"

        # 模拟当前时间为2024年3月
        with patch('datetime.datetime') as mock_datetime:
            mock_datetime.now.return_value = datetime.datetime(2024, 3, 15)
            mock_datetime.strptime = datetime.datetime.strptime

            period = calculate_current_period(start_date)
            self.assertGreater(period, 0)
            self.assertLessEqual(period, 12)  # 不超过12个月

    def test_trading_environment_validation(self):
        """测试交易环境验证"""
        # 初始化数据库
        init_database()
        load_strategy_status()

        # 测试正常环境
        result = validate_trading_environment(self.mock_context)
        # 由于是模拟环境，可能返回False，但不应该抛出异常
        self.assertIsInstance(result, bool)

    def test_error_handling(self):
        """测试错误处理"""
        # 测试安全执行函数
        def failing_function():
            raise ValueError("测试错误")

        result = safe_execute(failing_function, default_return="默认值", log_prefix="测试")
        self.assertEqual(result, "默认值")

    def test_performance_summary(self):
        """测试性能摘要"""
        # 初始化数据库
        init_database()
        load_strategy_status()

        # 获取性能摘要
        summary = get_strategy_performance_summary()

        # 验证摘要结构
        expected_keys = [
            'total_trades', 'successful_trades', 'failed_trades',
            'total_signals', 'valid_signals', 'filtered_signals',
            'current_phase', 'phase_duration_days'
        ]

        for key in expected_keys:
            self.assertIn(key, summary)

    def test_log_message(self):
        """测试日志记录"""
        # 初始化数据库
        init_database()

        # 记录日志
        log_message("INFO", "测试", "这是一条测试日志")

        # 验证日志是否记录到数据库
        cursor = g_db_connection.cursor()
        cursor.execute("SELECT COUNT(*) FROM trade_logs WHERE message = '这是一条测试日志'")
        count = cursor.fetchone()[0]
        self.assertEqual(count, 1)


class TestStrategyIntegration(unittest.TestCase):
    """策略集成测试"""

    def setUp(self):
        """测试前准备"""
        global DATABASE_PATH, g_db_connection, g_strategy_status
        DATABASE_PATH = "test_integration.db"

        if os.path.exists(DATABASE_PATH):
            os.remove(DATABASE_PATH)

        g_db_connection = None
        g_strategy_status = None

        # 创建模拟的ContextInfo对象
        self.mock_context = Mock()
        self.mock_context.accid = "test_account"
        self.mock_context.is_last_bar.return_value = True
        self.mock_context.run_count = 0

        # 模拟市场数据
        mock_data = Mock()
        mock_data.empty = False
        mock_data.__len__ = Mock(return_value=100)
        mock_data.iloc = Mock()
        mock_data.iloc.__getitem__ = Mock(return_value=10.0)
        mock_data.__getitem__ = Mock(return_value=[10.0] * 100)
        self.mock_context.get_market_data_ex.return_value = mock_data

    def tearDown(self):
        """测试后清理"""
        global g_db_connection

        if g_db_connection:
            g_db_connection.close()
            g_db_connection = None

        if os.path.exists(DATABASE_PATH):
            os.remove(DATABASE_PATH)

    @patch('value_averaging_strategy.passorder')
    def test_strategy_initialization(self, mock_passorder):
        """测试策略初始化"""
        # 测试初始化不抛出异常
        try:
            init(self.mock_context)
            self.assertTrue(True)  # 如果没有异常，测试通过
        except Exception as e:
            self.fail(f"策略初始化失败：{str(e)}")

    @patch('value_averaging_strategy.passorder')
    def test_handlebar_execution(self, mock_passorder):
        """测试主策略逻辑执行"""
        # 先初始化
        init(self.mock_context)

        # 测试handlebar执行不抛出异常
        try:
            handlebar(self.mock_context)
            self.assertTrue(True)  # 如果没有异常，测试通过
        except Exception as e:
            self.fail(f"策略执行失败：{str(e)}")


if __name__ == '__main__':
    print("开始运行价值平均+择时策略测试...")
    print("=" * 60)

    # 运行测试
    unittest.main(verbosity=2)