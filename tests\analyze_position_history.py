# -*- coding: utf-8 -*-
"""
分析持仓历史记录，找出2020-02-28卖出股数错误的原因
"""

def analyze_position_discrepancy():
    """分析持仓差异"""
    
    print("🔍 分析2020-02-28持仓差异")
    print("=" * 60)
    
    # 从Excel和数据库对比的关键时间点
    transactions = [
        # 格式：(日期, Excel持仓, Excel交易, 数据库交易, 价格)
        ("2019-12-31", 343721, -20021, 20021, 1.73),   # 正确
        ("2020-02-28", 323700, -37985, 32973, 1.995),  # 错误！
    ]
    
    print("📊 交易记录对比:")
    print("日期\t\tExcel持仓\tExcel交易\t数据库交易\t差异")
    print("-" * 60)
    
    for date, excel_pos, excel_trade, db_trade, price in transactions:
        diff = abs(excel_trade) - db_trade if excel_trade < 0 else excel_trade - db_trade
        status = "✅" if diff == 0 else "❌"
        print(f"{date}\t{excel_pos:,}\t\t{excel_trade:,}\t\t{db_trade:,}\t\t{diff:,} {status}")
    
    print(f"\n🔍 重点分析2020-02-28:")
    
    # 2020-02-28的详细分析
    date = "2020-02-28"
    excel_position_before = 343721  # 2019-12-31之后的持仓
    excel_sell = 37985
    db_sell = 32973
    price = 1.995
    
    # 计算期间可能的持仓变化
    print(f"2019-12-31后持仓: {excel_position_before:,}股")
    print(f"2020-02-28前持仓: 应该是 {excel_position_before - 20021:,}股 = {excel_position_before - 20021:,}股")
    
    # 如果数据库只能卖出32973股，说明当时持仓可能只有32973股
    implied_position = 32973
    print(f"数据库隐含持仓: {implied_position:,}股")
    
    # 计算差异
    position_diff = (excel_position_before - 20021) - implied_position
    print(f"持仓差异: {position_diff:,}股")
    
    print(f"\n🔍 可能的原因分析:")
    
    # 原因1：2019-12-31的卖出没有正确更新持仓
    print(f"1. 2019-12-31卖出20021股后，持仓更新有误")
    print(f"   正确持仓应该是: {excel_position_before - 20021:,}股")
    print(f"   但数据库可能记录为: {implied_position:,}股")
    
    # 原因2：中间有其他交易没有记录
    print(f"2. 2019-12-31到2020-02-28之间可能有未记录的交易")
    
    # 原因3：持仓查询逻辑有问题
    print(f"3. get_current_position函数可能返回了错误的持仓数据")
    
    # 验证计算
    print(f"\n🧮 验证计算:")
    target_amount = 57 * 10000  # 570000
    correct_position = excel_position_before - 20021  # 323700
    current_value = correct_position * price
    trade_amount = target_amount - current_value
    correct_sell = int(abs(trade_amount) / price)
    
    print(f"目标金额: {target_amount:,}元")
    print(f"正确持仓: {correct_position:,}股")
    print(f"当前价值: {current_value:,.2f}元")
    print(f"需要卖出: {abs(trade_amount):,.2f}元")
    print(f"应卖股数: {correct_sell:,}股")
    
    # 如果按错误持仓计算
    wrong_position = implied_position
    wrong_value = wrong_position * price
    wrong_trade_amount = target_amount - wrong_value
    wrong_sell = int(abs(wrong_trade_amount) / price) if wrong_trade_amount < 0 else 0
    
    print(f"\n❌ 如果按错误持仓{wrong_position:,}股计算:")
    print(f"当前价值: {wrong_value:,.2f}元")
    print(f"需要卖出: {abs(wrong_trade_amount):,.2f}元")
    print(f"计算卖出: {wrong_sell:,}股")
    
    if wrong_sell == db_sell:
        print(f"✅ 这解释了为什么数据库记录是{db_sell:,}股！")
        print(f"问题根源：持仓数据不准确")
    
    return {
        'correct_position': correct_position,
        'implied_position': implied_position,
        'position_diff': position_diff,
        'correct_sell': correct_sell,
        'actual_sell': db_sell,
        'sell_diff': correct_sell - db_sell
    }

def main():
    """主函数"""
    print("开始分析持仓历史...")
    
    try:
        result = analyze_position_discrepancy()
        
        print(f"\n🎯 分析结论:")
        print(f"正确持仓: {result['correct_position']:,}股")
        print(f"数据库隐含持仓: {result['implied_position']:,}股")
        print(f"持仓差异: {result['position_diff']:,}股")
        print(f"应卖股数: {result['correct_sell']:,}股")
        print(f"实际卖出: {result['actual_sell']:,}股")
        print(f"卖出差异: {result['sell_diff']:,}股")
        
        print(f"\n🔧 修复建议:")
        print(f"1. 检查 get_current_position 函数的查询逻辑")
        print(f"2. 确保每次交易后持仓记录正确更新")
        print(f"3. 添加持仓一致性检查机制")
        print(f"4. 在卖出前验证持仓数量是否足够")
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
