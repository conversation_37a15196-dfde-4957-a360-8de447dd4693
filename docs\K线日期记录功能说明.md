# K线日期记录功能说明

## 功能概述

在原有的信号过滤改进基础上，进一步增强了 `detect_signals` 方法，现在除了记录K线位置外，还会记录该K线对应的日期，格式为"YYYYMMDD"（如"20250807"）。

## 实现细节

### 1. 数据库结构扩展

#### 新增字段
在 `signal_history` 表中新增 `kline_date` 字段：
```sql
ALTER TABLE signal_history ADD COLUMN kline_date TEXT;  -- K线日期（YYYYMMDD格式）
```

#### 完整表结构
```sql
CREATE TABLE signal_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    signal_date TEXT NOT NULL,            -- 信号日期
    signal_type TEXT NOT NULL,            -- 信号类型：'ENTERLONG' 或 'EXITLONG'
    signal_price REAL NOT NULL,           -- 信号价格
    ema_value REAL NOT NULL,              -- EMA值
    bottom_line REAL,                     -- 底部线F1值（买入信号时）
    top_line REAL,                        -- 顶部线F2值（卖出信号时）
    kline_position INTEGER,               -- K线位置（barpos）
    kline_date TEXT,                      -- K线日期（YYYYMMDD格式）
    is_valid INTEGER NOT NULL,            -- 是否有效信号：1有效，0无效
    filter_reason TEXT,                   -- 过滤原因
    created_time TEXT NOT NULL           -- 创建时间
);
```

### 2. K线日期获取逻辑

#### 获取方法
在 `detect_signals()` 函数中，通过以下步骤获取K线日期：

```python
# 获取当前K线对应的日期（YYYYMMDD格式）
try:
    current_bar_timestamp = ContextInfo.get_bar_timetag(current_kline_position)
    if current_bar_timestamp:
        kline_datetime = datetime.datetime.fromtimestamp(current_bar_timestamp / 1000)
        current_kline_date = kline_datetime.strftime("%Y%m%d")
    else:
        # 如果无法获取K线时间戳，使用当前日期
        current_kline_date = datetime.datetime.now().strftime("%Y%m%d")
except Exception as e:
    log_message("WARNING", "信号检测", f"获取K线日期失败：{str(e)}")
    current_kline_date = datetime.datetime.now().strftime("%Y%m%d")
```

#### 关键步骤
1. **获取时间戳**：使用 `ContextInfo.get_bar_timetag(current_kline_position)` 获取当前K线的毫秒时间戳
2. **转换日期时间**：将毫秒时间戳转换为 `datetime` 对象
3. **格式化日期**：使用 `strftime("%Y%m%d")` 格式化为YYYYMMDD格式
4. **异常处理**：如果获取失败，使用当前系统日期作为备选

### 3. 信号记录更新

#### 信号详情结构
所有信号详情现在都包含K线日期信息：
```python
signal_details = {
    'signal_type': 'ENTERLONG',
    'signal_price': current_close,
    'ema_value': ema_value,
    'bottom_line': bottom_line,
    'top_line': None,
    'signal_time': current_time,
    'kline_position': current_kline_position,
    'kline_date': current_kline_date  # 新增字段
}
```

#### 数据库插入
更新了 `record_signal_to_db()` 函数：
```python
cursor.execute("""
    INSERT INTO signal_history
    (signal_date, signal_type, signal_price, ema_value, bottom_line, top_line,
     kline_position, kline_date, is_valid, filter_reason, created_time)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
""", (
    signal_details['signal_time'],
    signal_details['signal_type'],
    signal_details['signal_price'],
    signal_details['ema_value'],
    signal_details.get('bottom_line'),
    signal_details.get('top_line'),
    signal_details.get('kline_position'),
    signal_details.get('kline_date'),  # 新增字段
    1 if is_valid else 0,
    filter_reason,
    current_time
))
```

### 4. 自动升级机制

#### 数据库升级
`upgrade_database_schema()` 函数会自动检测并添加新字段：
```python
if 'kline_date' not in columns:
    print("添加kline_date字段到signal_history表...")
    cursor.execute("ALTER TABLE signal_history ADD COLUMN kline_date TEXT")
    print("kline_date字段添加成功")
```

## 使用场景

### 1. 数据分析
- 可以按日期分析信号分布
- 便于统计特定日期的信号情况
- 支持按日期范围查询信号

### 2. 调试和监控
- 快速定位特定日期的信号
- 验证信号时间的准确性
- 便于问题排查和分析

### 3. 报表生成
- 按日期生成信号统计报表
- 分析信号的时间分布特征
- 支持日期维度的数据展示

## 数据格式说明

### K线日期格式
- **格式**：YYYYMMDD
- **示例**：20250807（表示2025年8月7日）
- **长度**：固定8位数字
- **时区**：基于系统时区

### 存储位置
- **表名**：signal_history
- **字段名**：kline_date
- **数据类型**：TEXT
- **可空性**：允许为空（向后兼容）

## 测试验证

创建了完整的测试用例验证功能：
1. ✅ K线日期格式转换测试
2. ✅ 数据库存储和查询测试
3. ✅ 信号详情结构测试
4. ✅ 异常处理测试

## 向后兼容性

- 现有数据库会自动添加新字段
- 历史记录的 `kline_date` 字段为空，不影响功能
- 新产生的信号会自动包含K线日期信息
- 不影响现有的信号过滤逻辑

## 注意事项

1. **时区问题**：K线日期基于系统时区，确保服务器时区设置正确
2. **时间戳精度**：iQuant返回的是毫秒时间戳，需要除以1000转换
3. **异常处理**：如果无法获取K线时间戳，会使用当前系统日期作为备选
4. **数据一致性**：建议定期检查K线日期与信号日期的一致性
