# -*- coding: utf-8 -*-
"""
诊断交易未执行的问题
"""

import sqlite3
import datetime
from typing import Tuple

# 模拟配置参数
ENABLE_TIME_CONTROL = True
TRADE_TIME_CONTROL = "144500"  # 14:45:00
INVESTMENT_CYCLE = "1mon"
DATABASE_PATH = "gytrading2.db"

def check_time_control() -> Tuple[bool, str]:
    """检查时点控制"""
    try:
        current_time = datetime.datetime.now()
        current_time_str = current_time.strftime("%H%M%S")
        
        if not ENABLE_TIME_CONTROL:
            return (True, "时点控制已禁用")
        
        trade_hour = int(TRADE_TIME_CONTROL[:2])
        trade_minute = int(TRADE_TIME_CONTROL[2:4])
        trade_second = int(TRADE_TIME_CONTROL[4:6])
        
        trade_time = current_time.replace(hour=trade_hour, minute=trade_minute, second=trade_second, microsecond=0)
        
        if current_time >= trade_time:
            return (True, f"当前时间{current_time.strftime('%H:%M:%S')}已过交易时点{TRADE_TIME_CONTROL[:2]}:{TRADE_TIME_CONTROL[2:4]}:{TRADE_TIME_CONTROL[4:6]}")
        else:
            return (False, f"当前时间{current_time.strftime('%H:%M:%S')}未到交易时点{TRADE_TIME_CONTROL[:2]}:{TRADE_TIME_CONTROL[2:4]}:{TRADE_TIME_CONTROL[4:6]}")
    
    except Exception as e:
        return (True, f"时点检查异常：{str(e)}")

def check_adjustment_time():
    """检查调整时机"""
    try:
        # 检查今天是否为工作日
        current_date = datetime.datetime.now()
        if current_date.weekday() >= 5:  # 周末
            return (False, f"今天是{['周一','周二','周三','周四','周五','周六','周日'][current_date.weekday()]}，跳过周末")
        
        # 检查投资周期
        if INVESTMENT_CYCLE in ["日线", "1d"]:
            return (True, "日线周期，每日调整")
        elif INVESTMENT_CYCLE in ["月线", "1mon"]:
            # 简化的月线检查（实际逻辑更复杂）
            return (True, "月线周期，需要详细检查")
        else:
            return (False, f"未识别的投资周期：{INVESTMENT_CYCLE}")
    
    except Exception as e:
        return (False, f"调整时机检查异常：{str(e)}")

def check_today_trading_records():
    """检查当天交易记录"""
    try:
        connection = sqlite3.connect(DATABASE_PATH)
        cursor = connection.cursor()
        
        today = datetime.datetime.now().strftime("%Y-%m-%d")
        
        # 检查trade_orders表
        cursor.execute("""
            SELECT COUNT(*) FROM trade_orders
            WHERE DATE(order_date) = ?
            AND order_status IN ('SUCCESS', 'PENDING')
        """, (today,))
        
        orders_count = cursor.fetchone()[0]
        
        # 检查trade_task_queue表
        cursor.execute("""
            SELECT COUNT(*) FROM trade_task_queue
            WHERE DATE(created_time) = ?
            AND task_status IN ('COMPLETED', 'EXECUTING', 'WAITING_CALLBACK')
        """, (today,))
        
        tasks_count = cursor.fetchone()[0]
        
        connection.close()
        
        has_trading = orders_count > 0 or tasks_count > 0
        
        return (has_trading, f"当天交易记录：订单{orders_count}个，任务{tasks_count}个")
    
    except Exception as e:
        return (False, f"检查交易记录异常：{str(e)}")

def check_recent_logs():
    """检查最近的相关日志"""
    try:
        connection = sqlite3.connect(DATABASE_PATH)
        cursor = connection.cursor()
        
        # 查询最近的价值平均相关日志
        cursor.execute("""
            SELECT log_date, operation, message 
            FROM trade_logs 
            WHERE operation IN ('价值平均', '调整时机', '时点控制', '交易逻辑')
            ORDER BY created_time DESC 
            LIMIT 20
        """)
        
        logs = cursor.fetchall()
        connection.close()
        
        return logs
    
    except Exception as e:
        return [("ERROR", "日志查询", f"查询日志失败：{str(e)}")]

def diagnose_trading_issue():
    """诊断交易未执行的问题"""
    print("=" * 80)
    print("诊断交易未执行问题")
    print("=" * 80)
    
    print(f"当前时间：{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"当前配置：")
    print(f"  ENABLE_TIME_CONTROL = {ENABLE_TIME_CONTROL}")
    print(f"  TRADE_TIME_CONTROL = {TRADE_TIME_CONTROL}")
    print(f"  INVESTMENT_CYCLE = {INVESTMENT_CYCLE}")
    
    print("\n" + "=" * 50)
    print("1. 检查时点控制")
    print("=" * 50)
    
    time_allowed, time_reason = check_time_control()
    print(f"时点控制结果：{time_allowed}")
    print(f"原因：{time_reason}")
    
    if not time_allowed:
        print("❌ 时点控制阻止了交易执行")
        print("💡 解决方案：")
        print("   - 等待到14:45:00之后再运行")
        print("   - 或者设置 ENABLE_TIME_CONTROL = False")
        print("   - 或者修改 TRADE_TIME_CONTROL 为更早的时间")
    else:
        print("✅ 时点控制允许交易")
    
    print("\n" + "=" * 50)
    print("2. 检查调整时机")
    print("=" * 50)
    
    adjustment_allowed, adjustment_reason = check_adjustment_time()
    print(f"调整时机结果：{adjustment_allowed}")
    print(f"原因：{adjustment_reason}")
    
    if not adjustment_allowed:
        print("❌ 调整时机检查阻止了交易执行")
    else:
        print("✅ 调整时机允许交易")
    
    print("\n" + "=" * 50)
    print("3. 检查当天交易记录")
    print("=" * 50)
    
    has_traded, trade_reason = check_today_trading_records()
    print(f"当天交易检查结果：{has_traded}")
    print(f"详情：{trade_reason}")
    
    if has_traded:
        print("❌ 当天已有交易记录，防重复机制阻止了交易")
        print("💡 解决方案：")
        print("   - 检查是否确实需要再次交易")
        print("   - 或者清理当天的交易记录（谨慎操作）")
    else:
        print("✅ 当天无交易记录，允许交易")
    
    print("\n" + "=" * 50)
    print("4. 检查最近日志")
    print("=" * 50)
    
    recent_logs = check_recent_logs()
    print("最近20条相关日志：")
    for i, (log_date, operation, message) in enumerate(recent_logs[:10]):
        print(f"  {i+1:2d}. [{log_date}] {operation}: {message}")
    
    if len(recent_logs) > 10:
        print(f"  ... 还有{len(recent_logs)-10}条日志")
    
    print("\n" + "=" * 80)
    print("诊断总结")
    print("=" * 80)
    
    blocking_factors = []
    
    if not time_allowed:
        blocking_factors.append("时点控制")
    
    if not adjustment_allowed:
        blocking_factors.append("调整时机")
    
    if has_traded:
        blocking_factors.append("防重复机制")
    
    if blocking_factors:
        print("❌ 发现阻止交易的因素：")
        for factor in blocking_factors:
            print(f"   - {factor}")
        
        print("\n💡 建议的解决方案：")
        if "时点控制" in blocking_factors:
            print("   1. 等待到14:45:00之后，或禁用时点控制")
        if "调整时机" in blocking_factors:
            print("   2. 检查投资周期配置和当前日期")
        if "防重复机制" in blocking_factors:
            print("   3. 检查当天是否确实已经交易过")
    else:
        print("✅ 未发现明显的阻止因素")
        print("💡 可能的其他原因：")
        print("   - 价值平均计算结果为无需调整")
        print("   - 任务创建过程中出现异常")
        print("   - 建议查看完整的日志记录")

if __name__ == "__main__":
    try:
        diagnose_trading_issue()
    except Exception as e:
        print(f"诊断过程出错：{str(e)}")
        import traceback
        traceback.print_exc()
