# -*- coding: utf-8 -*-
"""
测试任务状态更新功能
验证 execute_async_buy_order 和 execute_async_sell_order 中的任务状态更新
"""

import os
import sys
import uuid

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_task_status_update():
    """测试任务状态更新功能"""
    try:
        print("开始测试任务状态更新功能")
        print("=" * 60)
        
        # 导入策略模块
        from aggressive_strategy import (
            init_database,
            TaskStatus,
            TaskType,
            g_db_connection,
            DATABASE_PATH
        )
        
        # 删除现有数据库文件（如果存在）
        if os.path.exists(DATABASE_PATH):
            os.remove(DATABASE_PATH)
            print(f"已删除现有数据库文件：{DATABASE_PATH}")
        
        # 初始化数据库
        print("\n--- 初始化数据库 ---")
        success = init_database()
        if not success:
            print("❌ 数据库初始化失败")
            return False
        
        # 手动创建一个测试任务
        print("\n--- 创建测试任务 ---")
        import sqlite3
        connection = sqlite3.connect(DATABASE_PATH)
        cursor = connection.cursor()
        
        # 插入测试任务
        test_uuid = str(uuid.uuid4())
        test_task_group_id = f"TEST_GROUP_{test_uuid[:8]}"
        
        cursor.execute("""
            INSERT INTO trade_task_queue (
                task_group_id, task_type, stock_code, target_shares, 
                target_amount, estimated_price, estimated_fees, 
                task_status, order_uuid, created_time
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            test_task_group_id,
            TaskType.BUY_ACTIVE_FUND_CASH.value,
            "159967.SZ",
            1000,
            1000.0,
            1.0,
            1.0,
            TaskStatus.PENDING.value,
            test_uuid,
            "2024-01-01 10:00:00"
        ))
        
        connection.commit()
        connection.close()
        
        print(f"✅ 创建测试任务：UUID={test_uuid}")
        
        # 测试异步买入订单状态更新
        print("\n--- 测试异步买入订单状态更新 ---")
        from aggressive_strategy import execute_async_buy_order
        
        # 模拟 ContextInfo
        class MockContextInfo:
            pass
        
        mock_context = MockContextInfo()
        
        # 执行异步买入（这会更新任务状态）
        order_id = execute_async_buy_order(
            mock_context, 
            "159967.SZ", 
            1000, 
            "测试买入", 
            test_uuid, 
            use_margin=False
        )
        
        if order_id:
            print(f"✅ 异步买入订单执行成功，订单ID：{order_id}")
            
            # 检查任务状态是否更新
            connection = sqlite3.connect(DATABASE_PATH)
            cursor = connection.cursor()
            
            cursor.execute("""
                SELECT task_status, order_id, target_shares 
                FROM trade_task_queue 
                WHERE order_uuid = ?
            """, (test_uuid,))
            
            result = cursor.fetchone()
            if result:
                task_status, db_order_id, target_shares = result
                print(f"✅ 任务状态已更新：")
                print(f"   状态：{task_status}")
                print(f"   订单ID：{db_order_id}")
                print(f"   目标股数：{target_shares}")
                
                if task_status == TaskStatus.WAITING_CALLBACK.value:
                    print("✅ 任务状态正确更新为 WAITING_CALLBACK")
                else:
                    print(f"❌ 任务状态错误：期望 {TaskStatus.WAITING_CALLBACK.value}，实际 {task_status}")
            else:
                print("❌ 未找到任务记录")
                
            connection.close()
        else:
            print("❌ 异步买入订单执行失败")
        
        # 测试异步卖出订单状态更新
        print("\n--- 测试异步卖出订单状态更新 ---")
        
        # 创建另一个测试任务
        test_uuid2 = str(uuid.uuid4())
        test_task_group_id2 = f"TEST_GROUP_{test_uuid2[:8]}"
        
        connection = sqlite3.connect(DATABASE_PATH)
        cursor = connection.cursor()
        
        cursor.execute("""
            INSERT INTO trade_task_queue (
                task_group_id, task_type, stock_code, target_shares, 
                target_amount, estimated_price, estimated_fees, 
                task_status, order_uuid, created_time
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            test_task_group_id2,
            TaskType.SELL_ACTIVE_FUND.value,
            "159967.SZ",
            500,
            500.0,
            1.0,
            1.0,
            TaskStatus.PENDING.value,
            test_uuid2,
            "2024-01-01 10:00:00"
        ))
        
        connection.commit()
        connection.close()
        
        print(f"✅ 创建第二个测试任务：UUID={test_uuid2}")
        
        # 执行异步卖出
        from aggressive_strategy import execute_async_sell_order
        
        order_id2 = execute_async_sell_order(
            mock_context, 
            "159967.SZ", 
            500, 
            "测试卖出", 
            test_uuid2
        )
        
        if order_id2:
            print(f"✅ 异步卖出订单执行成功，订单ID：{order_id2}")
            
            # 检查任务状态是否更新
            connection = sqlite3.connect(DATABASE_PATH)
            cursor = connection.cursor()
            
            cursor.execute("""
                SELECT task_status, order_id, target_shares 
                FROM trade_task_queue 
                WHERE order_uuid = ?
            """, (test_uuid2,))
            
            result = cursor.fetchone()
            if result:
                task_status, db_order_id, target_shares = result
                print(f"✅ 任务状态已更新：")
                print(f"   状态：{task_status}")
                print(f"   订单ID：{db_order_id}")
                print(f"   目标股数：{target_shares}")
                
                if task_status == TaskStatus.WAITING_CALLBACK.value:
                    print("✅ 任务状态正确更新为 WAITING_CALLBACK")
                else:
                    print(f"❌ 任务状态错误：期望 {TaskStatus.WAITING_CALLBACK.value}，实际 {task_status}")
            else:
                print("❌ 未找到任务记录")
                
            connection.close()
        else:
            print("❌ 异步卖出订单执行失败")
        
        print("\n" + "=" * 60)
        print("🎉 任务状态更新功能测试完成！")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败：{str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("激进版策略任务状态更新功能测试")
    print("=" * 60)
    
    try:
        if test_task_status_update():
            print("\n🎯 测试通过！任务状态更新功能正常：")
            print("   ✅ execute_async_buy_order 正确更新任务状态")
            print("   ✅ execute_async_sell_order 正确更新任务状态")
            print("   ✅ 任务状态正确设置为 WAITING_CALLBACK")
            print("   ✅ 订单ID正确记录到数据库")
        else:
            print("\n💥 测试失败！")
            
    except Exception as e:
        print(f"\n💥 测试过程中发生错误：{str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
