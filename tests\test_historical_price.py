#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试历史最高价获取功能
验证get_historical_highest_price方法的pandas索引修复
"""

import pandas as pd
import datetime
import numpy as np

def test_pandas_indexing_fix():
    """测试pandas索引修复"""
    print("=== 测试pandas索引修复 ===")
    
    # 创建模拟的月线数据
    dates = pd.date_range(start='2020-01-01', end='2025-01-01', freq='M')
    np.random.seed(42)  # 固定随机种子，确保结果可重复
    
    # 生成模拟价格数据，包含一个明显的最高点
    prices = np.random.uniform(1.0, 2.0, len(dates))
    prices[10] = 3.5  # 在第11个月设置最高价
    
    monthly_data = pd.DataFrame({
        'close': prices,
        'high': prices * 1.02  # 最高价略高于收盘价
    }, index=dates)
    
    print(f"创建了 {len(monthly_data)} 个月的模拟数据")
    print(f"数据时间范围：{monthly_data.index[0]} 到 {monthly_data.index[-1]}")
    
    # 测试修复后的逻辑
    try:
        # 找到最高价及其对应的日期
        close_data = monthly_data['close']
        max_high_idx = close_data.idxmax()  # 这返回的是索引值（日期），不是位置
        
        print(f"idxmax()返回的索引类型：{type(max_high_idx)}")
        print(f"idxmax()返回的值：{max_high_idx}")
        
        # 直接使用索引值获取价格和日期
        max_high_price = close_data.loc[max_high_idx]  # 使用.loc而不是.iloc
        max_high_date = max_high_idx  # idxmax()返回的就是索引值（日期）
        
        print(f"最高价：{max_high_price:.4f}")
        print(f"最高价日期：{max_high_date}")
        
        # 格式化日期
        if hasattr(max_high_date, 'strftime'):
            date_str = max_high_date.strftime("%Y-%m-%d")
        else:
            # 如果是字符串格式的日期，尝试转换
            try:
                date_obj = datetime.datetime.strptime(str(max_high_date)[:8], "%Y%m%d")
                date_str = date_obj.strftime("%Y-%m-%d")
            except:
                date_str = "2020-01-01"  # 默认值
        
        print(f"格式化后的日期：{date_str}")
        
        # 验证结果
        assert max_high_price == 3.5, f"最高价应为3.5，实际为{max_high_price}"
        assert date_str == "2020-11-30", f"最高价日期应为2020-11-30，实际为{date_str}"
        
        print("✅ pandas索引修复测试通过")
        
    except Exception as e:
        print(f"❌ 测试失败：{str(e)}")
        raise

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    # 测试用例1：空数据
    try:
        empty_data = pd.DataFrame({'close': []}, index=pd.DatetimeIndex([]))
        if len(empty_data) > 0:
            max_idx = empty_data['close'].idxmax()
            print(f"空数据测试失败：应该抛出异常，但返回了{max_idx}")
        else:
            print("✓ 空数据正确处理")
    except Exception as e:
        print(f"✓ 空数据正确抛出异常：{type(e).__name__}")
    
    # 测试用例2：单个数据点
    try:
        single_data = pd.DataFrame({
            'close': [1.5]
        }, index=pd.DatetimeIndex(['2025-01-01']))
        
        max_idx = single_data['close'].idxmax()
        max_price = single_data['close'].loc[max_idx]
        
        print(f"✓ 单个数据点测试：最高价={max_price}，日期={max_idx}")
        assert max_price == 1.5, f"单个数据点价格应为1.5，实际为{max_price}"
        
    except Exception as e:
        print(f"❌ 单个数据点测试失败：{str(e)}")
    
    # 测试用例3：所有价格相同
    try:
        same_prices = pd.DataFrame({
            'close': [2.0, 2.0, 2.0, 2.0]
        }, index=pd.date_range('2025-01-01', periods=4, freq='M'))
        
        max_idx = same_prices['close'].idxmax()
        max_price = same_prices['close'].loc[max_idx]
        
        print(f"✓ 相同价格测试：最高价={max_price}，日期={max_idx}")
        assert max_price == 2.0, f"相同价格应为2.0，实际为{max_price}"
        
    except Exception as e:
        print(f"❌ 相同价格测试失败：{str(e)}")
    
    print("✅ 边界情况测试完成")

def test_date_formatting():
    """测试日期格式化"""
    print("\n=== 测试日期格式化 ===")
    
    # 创建不同类型的日期索引
    test_dates = [
        pd.Timestamp('2025-08-07'),
        pd.Timestamp('2025-08-07 12:30:45'),
        datetime.datetime(2025, 8, 7, 12, 30, 45)
    ]
    
    for i, test_date in enumerate(test_dates):
        print(f"测试日期 {i+1}：{test_date} (类型：{type(test_date)})")
        
        # 格式化日期
        if hasattr(test_date, 'strftime'):
            date_str = test_date.strftime("%Y-%m-%d")
            print(f"  格式化结果：{date_str}")
            assert date_str == "2025-08-07", f"日期格式化错误：{date_str}"
        else:
            print(f"  无法格式化，类型不支持strftime")
    
    print("✅ 日期格式化测试通过")

if __name__ == "__main__":
    test_pandas_indexing_fix()
    test_edge_cases()
    test_date_formatting()
    print("\n🎉 所有历史价格相关测试通过！")
