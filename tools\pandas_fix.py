# -*- coding: gbk -*-
"""
pandas兼容性修复补丁
用于修复iQuant平台中pandas版本兼容性问题

使用方法：
在value_averaging_strategy.py文件开头添加：
from pandas_fix import *

作者：AI Assistant
创建时间：2025-01-29
"""

def safe_iloc(data, index):
    """
    安全的iloc访问，兼容旧版pandas

    Args:
        data: pandas Series或DataFrame
        index: 索引位置

    Returns:
        对应位置的值
    """
    try:
        if hasattr(data, 'iloc'):
            return data.iloc[index]
        else:
            # 旧版pandas兼容
            if hasattr(data, 'values'):
                values = data.values
                if isinstance(index, int):
                    return values[index]
                else:
                    return values[index]
            else:
                # 转换为列表访问
                data_list = list(data)
                return data_list[index]
    except Exception as e:
        print(f"safe_iloc访问失败：{str(e)}")
        # 最后的备用方案
        try:
            data_list = list(data)
            return data_list[index]
        except:
            return None

def safe_empty_check(data):
    """
    安全的empty检查，兼容旧版pandas

    Args:
        data: pandas DataFrame或Series

    Returns:
        bool: 是否为空
    """
    try:
        if hasattr(data, 'empty'):
            return data.empty
        else:
            return len(data) == 0
    except:
        return True