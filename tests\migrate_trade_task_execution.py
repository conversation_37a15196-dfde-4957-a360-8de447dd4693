#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库迁移脚本：删除 trade_task_execution 表，扩展 trade_task_log 表
"""

import sqlite3
import os
import json
from datetime import datetime

def migrate_database(db_path):
    """迁移单个数据库文件"""
    print(f"\n开始迁移数据库：{db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name IN ('trade_task_execution', 'trade_task_log')")
        tables = cursor.fetchall()
        table_names = [table[0] for table in tables]
        
        print(f"现有表：{table_names}")
        
        # 2. 如果 trade_task_execution 表存在，先备份数据
        execution_data = []
        if 'trade_task_execution' in table_names:
            print("备份 trade_task_execution 表数据...")
            cursor.execute("SELECT * FROM trade_task_execution")
            execution_data = cursor.fetchall()
            
            # 获取列名
            cursor.execute("PRAGMA table_info(trade_task_execution)")
            execution_columns = [col[1] for col in cursor.fetchall()]
            print(f"备份了 {len(execution_data)} 条执行记录")
        
        # 3. 检查 trade_task_log 表结构
        log_columns = []
        if 'trade_task_log' in table_names:
            cursor.execute("PRAGMA table_info(trade_task_log)")
            log_columns = [col[1] for col in cursor.fetchall()]
            print(f"当前 trade_task_log 表字段：{log_columns}")
        
        # 4. 如果 trade_task_log 表缺少新字段，需要重建表
        required_new_fields = ['execution_step', 'step_status', 'actual_shares', 'actual_price', 'actual_amount', 'actual_fees', 'callback_data']
        missing_fields = [field for field in required_new_fields if field not in log_columns]
        
        if missing_fields:
            print(f"需要添加字段：{missing_fields}")
            
            # 备份现有 trade_task_log 数据
            log_data = []
            if 'trade_task_log' in table_names:
                cursor.execute("SELECT * FROM trade_task_log")
                log_data = cursor.fetchall()
                print(f"备份了 {len(log_data)} 条日志记录")
            
            # 删除旧表
            if 'trade_task_log' in table_names:
                cursor.execute("DROP TABLE trade_task_log")
                print("删除了旧的 trade_task_log 表")
            
            # 创建新的 trade_task_log 表
            cursor.execute("""
                CREATE TABLE trade_task_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_id INTEGER,
                    task_group_id TEXT,
                    log_level TEXT NOT NULL,
                    log_category TEXT NOT NULL,
                    log_message TEXT NOT NULL,
                    extra_data TEXT,
                    log_time TEXT NOT NULL,
                    -- 以下字段来自原 trade_task_execution 表
                    execution_step TEXT,
                    step_status TEXT,
                    actual_shares INTEGER,
                    actual_price REAL,
                    actual_amount REAL,
                    actual_fees REAL,
                    callback_data TEXT,
                    FOREIGN KEY (task_id) REFERENCES trade_task_queue (id)
                )
            """)
            print("创建了新的 trade_task_log 表")
            
            # 恢复原有日志数据
            if log_data:
                for row in log_data:
                    cursor.execute("""
                        INSERT INTO trade_task_log 
                        (task_id, task_group_id, log_level, log_category, log_message, extra_data, log_time)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, row[1:])  # 跳过 id 字段
                print(f"恢复了 {len(log_data)} 条日志记录")
        
        # 5. 迁移 trade_task_execution 数据到 trade_task_log
        if execution_data:
            print("迁移 trade_task_execution 数据到 trade_task_log...")
            
            for row in execution_data:
                # 解析执行记录数据
                exec_dict = dict(zip(execution_columns, row))
                
                # 获取任务组ID
                cursor.execute("SELECT task_group_id FROM trade_task_queue WHERE id = ?", (exec_dict['task_id'],))
                result = cursor.fetchone()
                task_group_id = result[0] if result else None
                
                # 插入到 trade_task_log
                cursor.execute("""
                    INSERT INTO trade_task_log 
                    (task_id, task_group_id, log_level, log_category, log_message, 
                     log_time, execution_step, step_status, actual_shares, actual_price,
                     actual_amount, actual_fees, callback_data)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    exec_dict['task_id'],
                    task_group_id,
                    'INFO',
                    'EXECUTION_STEP',
                    f"执行步骤：{exec_dict.get('execution_step', '')}",
                    exec_dict.get('execution_time', datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
                    exec_dict.get('execution_step'),
                    exec_dict.get('step_status'),
                    exec_dict.get('actual_shares'),
                    exec_dict.get('actual_price'),
                    exec_dict.get('actual_amount'),
                    exec_dict.get('actual_fees'),
                    exec_dict.get('callback_data')
                ))
            
            print(f"迁移了 {len(execution_data)} 条执行记录")
        
        # 6. 删除 trade_task_execution 表
        if 'trade_task_execution' in table_names:
            cursor.execute("DROP TABLE trade_task_execution")
            print("删除了 trade_task_execution 表")
        
        # 7. 删除相关索引
        try:
            cursor.execute("DROP INDEX IF EXISTS idx_trade_task_execution_task_id")
            cursor.execute("DROP INDEX IF EXISTS idx_trade_task_execution_time")
            print("删除了 trade_task_execution 相关索引")
        except:
            pass
        
        # 提交更改
        conn.commit()
        conn.close()
        
        print(f"✅ 数据库 {db_path} 迁移完成")
        return True
        
    except Exception as e:
        print(f"❌ 迁移数据库 {db_path} 失败：{e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("trade_task_execution 表删除和数据迁移")
    print("=" * 60)
    
    # 查找所有数据库文件
    db_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.db'):
                db_files.append(os.path.join(root, file))
    
    print(f"找到 {len(db_files)} 个数据库文件：")
    for db_file in db_files:
        print(f"  {db_file}")
    
    # 迁移每个数据库
    success_count = 0
    for db_file in db_files:
        if migrate_database(db_file):
            success_count += 1
    
    print(f"\n迁移完成：{success_count}/{len(db_files)} 个数据库成功迁移")

if __name__ == "__main__":
    main()
