# -*- coding: utf-8 -*-

from datetime import datetime
import talib
import numpy as np

def main():
    # ts = 1494172800000
    # print(datetime.fromtimestamp(ts/1000))
    close_prices = np.array([10.0, 10.5, 11.0, 10.8, 11.2, 11.5, 11.3, 11.8, 12.0, 11.9])
    expma = talib.EMA(close_prices, timeperiod=5)

    # 去除 NaN 值
    valid_expma = expma[~np.isnan(expma)]
    print("有效的 EXPMA 值:", valid_expma)


if __name__ == "__main__":
    main()
