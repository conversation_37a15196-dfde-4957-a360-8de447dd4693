# -*- coding: utf-8 -*-
"""
测试取消任务的重试计数逻辑
"""

def test_cancel_logic():
    """测试取消任务时的retry_count设置"""
    print("=" * 60)
    print("测试取消任务的重试计数逻辑")
    print("=" * 60)
    
    MAX_RETRY_COUNT = 3
    
    # 模拟场景：卖买关系，卖出任务失败，需要取消买入任务
    scenarios = [
        {
            'name': '正常取消场景',
            'description': '卖出任务失败(retry_count=3)，取消买入任务',
            'steps': [
                '1. 卖出任务失败，retry_count达到3',
                '2. 检测到买入任务为不同类型',
                '3. 取消买入任务，设置retry_count=3',
                '4. 买入任务不会被重试查找到'
            ]
        }
    ]
    
    for scenario in scenarios:
        print(f"\n{scenario['name']}:")
        print(f"描述：{scenario['description']}")
        
        for step in scenario['steps']:
            print(f"  {step}")
        
        # 验证取消后的任务状态
        cancelled_task_status = 'FAILED'
        cancelled_retry_count = MAX_RETRY_COUNT
        
        # 验证这个任务不会被重试查找到
        can_be_retried = (cancelled_task_status == 'FAILED' and 
                         cancelled_retry_count < MAX_RETRY_COUNT)
        
        print(f"\n  验证结果：")
        print(f"    任务状态：{cancelled_task_status}")
        print(f"    重试计数：{cancelled_retry_count}")
        print(f"    会被重试查找：{can_be_retried}")
        
        assert not can_be_retried, "被取消的任务不应该被重试查找到"
        print(f"    ✓ 验证通过：被取消的任务不会被重试")
    
    print("\n" + "=" * 60)
    print("取消任务逻辑验证通过！")
    print("=" * 60)

def test_retry_query_logic():
    """测试重试查询逻辑"""
    print("=" * 60)
    print("测试重试查询逻辑")
    print("=" * 60)
    
    MAX_RETRY_COUNT = 3
    
    # 模拟不同状态的任务
    tasks = [
        {
            'name': '正常失败任务',
            'status': 'FAILED',
            'retry_count': 1,
            'should_be_found': True,
            'description': '失败任务，重试次数未达上限'
        },
        {
            'name': '达到重试上限的任务',
            'status': 'FAILED', 
            'retry_count': 3,
            'should_be_found': False,
            'description': '失败任务，重试次数已达上限'
        },
        {
            'name': '被取消的任务',
            'status': 'FAILED',
            'retry_count': 3,  # 取消时设置为MAX_RETRY_COUNT
            'should_be_found': False,
            'description': '被取消的任务，retry_count设置为上限'
        },
        {
            'name': '完成的任务',
            'status': 'COMPLETED',
            'retry_count': -1,
            'should_be_found': False,
            'description': '已完成任务'
        },
        {
            'name': '执行中的任务',
            'status': 'EXECUTING',
            'retry_count': -1,
            'should_be_found': False,
            'description': '执行中任务'
        }
    ]
    
    for task in tasks:
        print(f"\n{task['name']}:")
        print(f"  {task['description']}")
        print(f"  状态：{task['status']}")
        print(f"  重试计数：{task['retry_count']}")
        
        # 模拟重试查询条件
        condition1 = task['status'] == 'FAILED'
        condition2 = task['retry_count'] < MAX_RETRY_COUNT
        will_be_found = condition1 and condition2
        
        print(f"  条件1（状态为FAILED）：{condition1}")
        print(f"  条件2（retry_count < {MAX_RETRY_COUNT}）：{condition2}")
        print(f"  会被查找到：{will_be_found}")
        print(f"  预期结果：{task['should_be_found']}")
        
        assert will_be_found == task['should_be_found'], f"重试查询逻辑错误"
        print(f"  ✓ 验证通过")
    
    print("\n" + "=" * 60)
    print("重试查询逻辑验证通过！")
    print("=" * 60)

def test_complete_scenario():
    """测试完整场景"""
    print("=" * 60)
    print("测试完整场景：卖买任务链失败处理")
    print("=" * 60)
    
    MAX_RETRY_COUNT = 3
    
    print("场景：SELL_510720 → BUY_159915_CASH")
    print("1. 卖出任务重试3次后失败")
    print("2. 检测到买入任务为不同类型")
    print("3. 取消买入任务")
    
    # 模拟卖出任务状态
    sell_task = {
        'status': 'FAILED',
        'retry_count': 3,  # 达到上限
        'type': 'SELL_510720'
    }
    
    # 模拟买入任务状态（取消前）
    buy_task_before = {
        'status': 'PENDING',
        'retry_count': -1,
        'type': 'BUY_159915_CASH'
    }
    
    # 模拟买入任务状态（取消后）
    buy_task_after = {
        'status': 'FAILED',
        'retry_count': MAX_RETRY_COUNT,  # 设置为上限
        'type': 'BUY_159915_CASH'
    }
    
    print(f"\n卖出任务最终状态：")
    print(f"  状态：{sell_task['status']}")
    print(f"  重试计数：{sell_task['retry_count']}")
    print(f"  类型：{sell_task['type']}")
    
    print(f"\n买入任务取消前：")
    print(f"  状态：{buy_task_before['status']}")
    print(f"  重试计数：{buy_task_before['retry_count']}")
    
    print(f"\n买入任务取消后：")
    print(f"  状态：{buy_task_after['status']}")
    print(f"  重试计数：{buy_task_after['retry_count']}")
    
    # 验证取消后的任务不会被重试
    will_be_retried = (buy_task_after['status'] == 'FAILED' and 
                      buy_task_after['retry_count'] < MAX_RETRY_COUNT)
    
    print(f"\n验证：买入任务是否会被重试查找到：{will_be_retried}")
    assert not will_be_retried, "被取消的任务不应该被重试"
    print("✓ 验证通过：被取消的任务不会被重试")
    
    print("\n" + "=" * 60)
    print("完整场景验证通过！")
    print("=" * 60)

if __name__ == "__main__":
    print("开始测试取消任务的重试计数逻辑...")
    
    try:
        test_cancel_logic()
        test_retry_query_logic()
        test_complete_scenario()
        print("\n🎉 所有测试通过！修复后的逻辑正确！")
    except Exception as e:
        print(f"\n❌ 测试失败：{str(e)}")
        import traceback
        traceback.print_exc()
