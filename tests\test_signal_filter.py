#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试信号过滤功能
验证基于K线距离的信号过滤逻辑
"""

import sqlite3
import datetime
import os
import sys

# 添加当前目录到路径，以便导入策略模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_database():
    """创建测试数据库"""
    # 删除已存在的测试数据库
    if os.path.exists('test_signal_filter.db'):
        os.remove('test_signal_filter.db')
    
    conn = sqlite3.connect('test_signal_filter.db')
    cursor = conn.cursor()
    
    # 创建信号历史表（包含新的kline_position和kline_date字段）
    cursor.execute("""
        CREATE TABLE signal_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            signal_date TEXT NOT NULL,
            signal_type TEXT NOT NULL,
            signal_price REAL NOT NULL,
            ema_value REAL NOT NULL,
            bottom_line REAL,
            top_line REAL,
            kline_position INTEGER,
            kline_date TEXT,
            is_valid INTEGER NOT NULL,
            filter_reason TEXT,
            created_time TEXT NOT NULL
        )
    """)
    
    conn.commit()
    return conn

def test_signal_filter_logic():
    """测试信号过滤逻辑"""
    print("=== 测试信号过滤功能 ===")
    
    # 创建测试数据库
    conn = create_test_database()
    cursor = conn.cursor()
    
    # 模拟check_signal_filter函数的核心逻辑
    def check_signal_filter_test(signal_type: str, current_kline_position: int):
        """测试版本的信号过滤函数"""
        # 过滤周期配置
        BUY_SIGNAL_FILTER_PERIODS = 8
        SELL_SIGNAL_FILTER_PERIODS = 10
        
        if signal_type == 'ENTERLONG':
            filter_periods = BUY_SIGNAL_FILTER_PERIODS
        elif signal_type == 'EXITLONG':
            filter_periods = SELL_SIGNAL_FILTER_PERIODS
        else:
            return (False, f"未知信号类型：{signal_type}")
        
        # 查询最近一次相同类型的有效信号
        cursor.execute("""
            SELECT signal_date, signal_type, kline_position FROM signal_history
            WHERE signal_type = ? AND is_valid = 1
            ORDER BY signal_date DESC
            LIMIT 1
        """, (signal_type,))
        
        recent_signal = cursor.fetchone()
        
        if recent_signal is None:
            return (True, None)
        
        last_kline_position = recent_signal[2]
        
        if last_kline_position is None:
            return (True, None)
        
        # 计算K线距离
        kline_distance = current_kline_position - last_kline_position

        if kline_distance <= filter_periods:
            return (False, f"距离上次{signal_type}信号仅{kline_distance}根K线，小于等于过滤周期{filter_periods}根K线")
        else:
            return (True, None)
    
    # 测试用例1：没有历史信号，应该允许通过
    print("\n测试用例1：没有历史信号")
    is_valid, reason = check_signal_filter_test('ENTERLONG', 100)
    print(f"结果：{is_valid}, 原因：{reason}")
    assert is_valid == True, "没有历史信号时应该允许通过"
    
    # 插入第一个买入信号
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    cursor.execute("""
        INSERT INTO signal_history
        (signal_date, signal_type, signal_price, ema_value, kline_position, kline_date, is_valid, created_time)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    """, (current_time, 'ENTERLONG', 10.0, 10.5, 100, '20250807', 1, current_time))
    conn.commit()
    
    # 测试用例2：距离太近，应该被过滤
    print("\n测试用例2：距离太近（5根K线，小于8根）")
    is_valid, reason = check_signal_filter_test('ENTERLONG', 105)
    print(f"结果：{is_valid}, 原因：{reason}")
    assert is_valid == False, "距离太近时应该被过滤"
    assert "5根K线" in reason, "过滤原因应该包含距离信息"
    
    # 测试用例3：距离刚好等于阈值，应该被过滤
    print("\n测试用例3：距离等于阈值（8根K线）")
    is_valid, reason = check_signal_filter_test('ENTERLONG', 108)
    print(f"结果：{is_valid}, 原因：{reason}")
    assert is_valid == False, "距离等于阈值时应该被过滤"
    
    # 测试用例4：距离超过阈值，应该允许通过
    print("\n测试用例4：距离超过阈值（9根K线，大于8根）")
    is_valid, reason = check_signal_filter_test('ENTERLONG', 109)
    print(f"结果：{is_valid}, 原因：{reason}")
    assert is_valid == True, "距离超过阈值时应该允许通过"
    
    # 测试用例5：不同信号类型互不影响
    print("\n测试用例5：不同信号类型（卖出信号）")
    is_valid, reason = check_signal_filter_test('EXITLONG', 105)
    print(f"结果：{is_valid}, 原因：{reason}")
    assert is_valid == True, "不同信号类型应该互不影响"
    
    # 插入卖出信号
    cursor.execute("""
        INSERT INTO signal_history
        (signal_date, signal_type, signal_price, ema_value, kline_position, kline_date, is_valid, created_time)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    """, (current_time, 'EXITLONG', 12.0, 10.5, 105, '20250808', 1, current_time))
    conn.commit()
    
    # 测试用例6：卖出信号的过滤（阈值是10根K线）
    print("\n测试用例6：卖出信号距离太近（8根K线，小于10根）")
    is_valid, reason = check_signal_filter_test('EXITLONG', 113)
    print(f"结果：{is_valid}, 原因：{reason}")
    assert is_valid == False, "卖出信号距离太近时应该被过滤"
    
    print("\n测试用例7：卖出信号距离足够（15根K线，大于10根）")
    is_valid, reason = check_signal_filter_test('EXITLONG', 120)
    print(f"结果：{is_valid}, 原因：{reason}")
    assert is_valid == True, "卖出信号距离足够时应该允许通过"
    
    conn.close()
    
    # 清理测试数据库
    if os.path.exists('test_signal_filter.db'):
        os.remove('test_signal_filter.db')
    
    print("\n✅ 所有测试用例通过！信号过滤逻辑正确。")

if __name__ == "__main__":
    test_signal_filter_logic()
