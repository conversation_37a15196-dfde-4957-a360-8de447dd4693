#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中的错误日志
"""

import sqlite3

# 数据库配置
DATABASE_PATH = "gytrading2.db"

def check_error_logs():
    """
    检查数据库中的错误日志
    """
    try:
        connection = sqlite3.connect(DATABASE_PATH)
        cursor = connection.cursor()
        
        print("检查数据库中的错误日志...")
        
        # 检查包含 "no such table: trade_execution_log" 的错误记录
        cursor.execute("SELECT COUNT(*) FROM trade_logs WHERE message LIKE '%no such table: trade_execution_log%'")
        count = cursor.fetchone()[0]
        print(f"包含 'no such table: trade_execution_log' 错误的记录数量: {count}")
        
        if count > 0:
            cursor.execute("""
                SELECT log_date, message 
                FROM trade_logs 
                WHERE message LIKE '%no such table: trade_execution_log%' 
                ORDER BY log_date DESC 
                LIMIT 5
            """)
            records = cursor.fetchall()
            print("最近的5条错误记录:")
            for record in records:
                print(f"  {record[0]}: {record[1]}")
        
        # 检查所有错误类型的记录
        cursor.execute("SELECT COUNT(*) FROM trade_logs WHERE log_type = 'ERROR'")
        error_count = cursor.fetchone()[0]
        print(f"\n总错误记录数量: {error_count}")
        
        if error_count > 0:
            cursor.execute("""
                SELECT log_date, operation, message 
                FROM trade_logs 
                WHERE log_type = 'ERROR' 
                ORDER BY log_date DESC 
                LIMIT 10
            """)
            records = cursor.fetchall()
            print("最近的10条错误记录:")
            for record in records:
                print(f"  {record[0]} [{record[1]}]: {record[2]}")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"检查错误日志失败：{str(e)}")

def main():
    """
    主函数
    """
    print("=" * 50)
    print("错误日志检查脚本")
    print("=" * 50)
    
    check_error_logs()

if __name__ == "__main__":
    main()
