# -*- coding: utf-8 -*-
"""
简化的trade_logs优化测试
"""

import sqlite3
import datetime

def test_optimized_table():
    """测试优化后的表结构"""
    print("=== 测试优化后的trade_logs表结构 ===")
    
    # 创建内存数据库
    conn = sqlite3.connect(":memory:")
    cursor = conn.cursor()
    
    # 创建优化后的表
    cursor.execute("""
        CREATE TABLE trade_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            log_date TEXT NOT NULL,               -- 系统时间
            kline_date TEXT,                      -- K线时间
            log_type TEXT NOT NULL,               -- 日志类型
            operation TEXT NOT NULL,              -- 操作类型
            message TEXT NOT NULL,                -- 消息
            details TEXT,                         -- 详细信息
            is_backtest INTEGER DEFAULT 0,       -- 回测标识
            created_time TEXT NOT NULL           -- 创建时间
        )
    """)
    
    # 插入测试数据
    test_data = [
        # 回测数据
        ("2025-08-13 10:00:00", "2024-08-30 09:30:00", "INFO", "价值平均", "执行价值平均策略", None, 1, "2025-08-13 10:00:00"),
        ("2025-08-13 10:01:00", "2024-08-30 09:31:00", "INFO", "价值平均买入", "买入1000股", '{"shares": 1000}', 1, "2025-08-13 10:01:00"),
        ("2025-08-13 10:02:00", "2024-09-30 09:30:00", "INFO", "价值平均", "执行价值平均策略", None, 1, "2025-08-13 10:02:00"),
        # 实盘数据
        ("2025-08-13 10:03:00", "2025-08-13 10:03:00", "INFO", "阶段切换", "切换到激活期", None, 0, "2025-08-13 10:03:00"),
    ]
    
    cursor.executemany("""
        INSERT INTO trade_logs (log_date, kline_date, log_type, operation, message, details, is_backtest, created_time)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    """, test_data)
    
    conn.commit()
    
    print("✅ 表创建和数据插入成功")
    
    # 测试查询
    print("\n--- 查询所有数据 ---")
    cursor.execute("SELECT id, kline_date, operation, message, is_backtest FROM trade_logs ORDER BY kline_date")
    results = cursor.fetchall()
    
    print(f"{'ID':<3} {'K线时间':<20} {'操作':<12} {'回测':<4} {'消息'}")
    print("-" * 60)
    
    for row in results:
        id_, kline_date, operation, message, is_backtest = row
        backtest_flag = "是" if is_backtest else "否"
        print(f"{id_:<3} {kline_date:<20} {operation:<12} {backtest_flag:<4} {message}")
    
    # 测试按K线日期查询
    print("\n--- 查询2024年8月的数据 ---")
    cursor.execute("""
        SELECT id, kline_date, operation, message 
        FROM trade_logs 
        WHERE DATE(kline_date) BETWEEN '2024-08-01' AND '2024-08-31'
        ORDER BY kline_date
    """)
    
    august_results = cursor.fetchall()
    print(f"找到 {len(august_results)} 条2024年8月的记录")
    for row in august_results:
        print(f"  {row[1]} - {row[2]}: {row[3]}")
    
    # 测试按操作类型查询
    print("\n--- 查询价值平均相关操作 ---")
    cursor.execute("""
        SELECT id, kline_date, operation, message 
        FROM trade_logs 
        WHERE operation LIKE '%价值平均%'
        ORDER BY kline_date
    """)
    
    va_results = cursor.fetchall()
    print(f"找到 {len(va_results)} 条价值平均相关记录")
    for row in va_results:
        print(f"  {row[1]} - {row[2]}: {row[3]}")
    
    conn.close()
    return True

def main():
    """主函数"""
    print("开始测试trade_logs表优化...")
    
    try:
        if test_optimized_table():
            print("\n🎉 trade_logs表优化测试成功！")
            print("\n优化总结:")
            print("1. ✅ 新增kline_date字段 - 记录K线时间")
            print("2. ✅ 新增is_backtest字段 - 区分回测/实盘")
            print("3. ✅ 支持按K线日期查询 - 便于回测分析")
            print("4. ✅ 支持按操作类型查询 - 便于功能分析")
            
            print("\n查询示例:")
            print("- 查询回测数据: WHERE is_backtest = 1")
            print("- 查询某日数据: WHERE DATE(kline_date) = '2024-08-30'")
            print("- 查询某月数据: WHERE DATE(kline_date) BETWEEN '2024-08-01' AND '2024-08-31'")
            print("- 查询特定操作: WHERE operation = '价值平均'")
            
            return True
        else:
            print("❌ 测试失败")
            return False
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return False

if __name__ == "__main__":
    main()
